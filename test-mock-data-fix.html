<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock Data Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 400px; }
        .console-output { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Mock Data Fix Test</h1>
    <p>This test verifies that real AI suggestions are used when a valid API key is present.</p>
    
    <div class="test-section">
        <h2>Current AI Status</h2>
        <button onclick="checkAIStatus()">Check AI Status</button>
        <div id="ai-status"></div>
    </div>

    <div class="test-section">
        <h2>Test API Key Setup</h2>
        <p>Enter a test API key to simulate having a valid key:</p>
        <input type="text" id="test-api-key" placeholder="sk-or-v1-test-key-12345" value="sk-or-v1-test-key-12345">
        <button onclick="setTestKey()">Set Test Key</button>
        <button onclick="clearTestKey()">Clear Test Key</button>
        <div id="key-status"></div>
    </div>

    <div class="test-section">
        <h2>Test AI Suggestions</h2>
        <button onclick="testSuggestions()">Generate AI Suggestions</button>
        <div id="suggestions-result"></div>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <div id="console-output" class="console-output"></div>
    </div>

    <script src="ai-service.js"></script>
    <script>
        let originalConsoleLog = console.log;
        let consoleOutput = document.getElementById('console-output');

        // Capture console.log output
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            consoleOutput.innerHTML += args.join(' ') + '<br>';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        function checkAIStatus() {
            const statusDiv = document.getElementById('ai-status');
            statusDiv.innerHTML = '';

            if (!window.AIService) {
                statusDiv.innerHTML = '<span class="error">❌ AI Service not available</span>';
                return;
            }

            const aiService = new window.AIService();
            const status = aiService.getReadinessStatus();

            statusDiv.innerHTML = `
                <div><strong>AI Service Status:</strong></div>
                <div>• AI Enabled: ${status.aiEnabled ? '✅' : '❌'}</div>
                <div>• Has API Key: ${status.hasApiKey ? '✅' : '❌'}</div>
                <div>• API Key: ${status.apiKeyValue}</div>
                <div>• Is Demo Key: ${status.isDemoKey ? '❌' : '✅'}</div>
                <div>• Valid Format: ${status.isValidFormat ? '✅' : '❌'}</div>
                <div><strong>• Ready for AI: ${status.isReady ? '✅ YES' : '❌ NO'}</strong></div>
            `;
        }

        function setTestKey() {
            const key = document.getElementById('test-api-key').value.trim();
            const statusDiv = document.getElementById('key-status');

            if (!key) {
                statusDiv.innerHTML = '<span class="error">Please enter a test key</span>';
                return;
            }

            try {
                localStorage.setItem('openrouter_api_key', key);
                statusDiv.innerHTML = `<span class="success">✅ Test key set: ${key}</span>`;
                console.log('Test API key set:', key);
                
                // Check status after setting key
                setTimeout(checkAIStatus, 100);
            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ Error setting key: ${error.message}</span>`;
            }
        }

        function clearTestKey() {
            localStorage.removeItem('openrouter_api_key');
            document.getElementById('key-status').innerHTML = '<span class="info">🗑️ Test key cleared</span>';
            console.log('Test API key cleared');
            
            // Check status after clearing key
            setTimeout(checkAIStatus, 100);
        }

        function testSuggestions() {
            const resultDiv = document.getElementById('suggestions-result');
            resultDiv.innerHTML = '<div>Testing AI suggestions...</div>';

            if (!window.AIService) {
                resultDiv.innerHTML = '<span class="error">❌ AI Service not available</span>';
                return;
            }

            const aiService = new window.AIService();
            console.log('Testing AI suggestions with current setup...');
            
            // Check if AI is ready
            const isReady = aiService.isReady();
            console.log('AI isReady():', isReady);

            if (isReady) {
                resultDiv.innerHTML = `
                    <div class="success">✅ AI is ready! Real AI suggestions would be used.</div>
                    <div>This means when you reload the main app, you should see real AI suggestions instead of mock data.</div>
                `;
                console.log('SUCCESS: AI is ready for real suggestions');
            } else {
                resultDiv.innerHTML = `
                    <div class="error">❌ AI is not ready. Mock suggestions would be used.</div>
                    <div>Check the AI status above to see what's missing.</div>
                `;
                console.log('ISSUE: AI is not ready, would use mock suggestions');
            }

            // Test the actual suggestion logic
            try {
                if (isReady) {
                    console.log('Would call generateAISuggestionsReal()');
                    resultDiv.innerHTML += '<div class="info">📡 Would make real API call to generate suggestions</div>';
                } else {
                    console.log('Would call generateAISuggestions() (mock)');
                    resultDiv.innerHTML += '<div class="info">🎭 Would use mock suggestions</div>';
                }
            } catch (error) {
                console.error('Error testing suggestions:', error);
                resultDiv.innerHTML += `<div class="error">Error: ${error.message}</div>`;
            }
        }

        // Run initial check
        window.addEventListener('load', () => {
            console.log('=== Mock Data Fix Test Started ===');
            checkAIStatus();
        });
    </script>
</body>
</html>
