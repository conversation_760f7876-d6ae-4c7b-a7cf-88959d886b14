<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Todo List</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-in-out',
                        'slide-in': 'slideIn 0.3s ease-out',
                        'bounce-in': 'bounceIn 0.5s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideIn: {
                            '0%': { transform: 'translateY(-10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        bounceIn: {
                            '0%': { transform: 'scale(0.3)', opacity: '0' },
                            '50%': { transform: 'scale(1.05)' },
                            '70%': { transform: 'scale(0.9)' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .task-item {
            transition: all 0.3s ease;
        }
        .task-item:hover {
            transform: translateY(-2px);
        }
        .task-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }
        .drop-zone {
            border: 2px dashed #3b82f6;
            background-color: rgba(59, 130, 246, 0.1);
        }
        .priority-high {
            border-left: 4px solid #ef4444;
        }
        .priority-medium {
            border-left: 4px solid #f59e0b;
        }
        .priority-low {
            border-left: 4px solid #10b981;
        }
        .overdue {
            background-color: rgba(239, 68, 68, 0.1);
        }
        .due-today {
            background-color: rgba(245, 158, 11, 0.1);
        }
        .completed-task {
            opacity: 0.6;
        }
        .completed-task .task-text {
            text-decoration: line-through;
        }
        .recording {
            animation: pulse 1.5s infinite;
            background-color: #ef4444 !important;
            color: white !important;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .task-item.urgent {
            border-left: 4px solid #dc2626;
            background: linear-gradient(90deg, rgba(220, 38, 38, 0.1) 0%, transparent 100%);
        }
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
        }
        .dark .glass-effect {
            background: rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-4xl mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-primary-500 p-2 rounded-lg">
                        <i class="fas fa-tasks text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Advanced Todo</h1>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Stay organized and productive</p>
                    </div>
                </div>
                
                <!-- Navigation & Controls -->
                <div class="flex items-center space-x-2">
                    <!-- View Navigation -->
                    <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                        <button id="tasks-view" class="px-3 py-1 text-sm rounded-md bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm transition-all" title="Tasks">
                            <i class="fas fa-tasks"></i>
                        </button>
                        <button id="analytics-view" class="px-3 py-1 text-sm rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-all" title="Analytics">
                            <i class="fas fa-chart-bar"></i>
                        </button>
                        <button id="habits-view" class="px-3 py-1 text-sm rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-all" title="Habits">
                            <i class="fas fa-calendar-check"></i>
                        </button>
                        <button id="goals-view" class="px-3 py-1 text-sm rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-all" title="Goals">
                            <i class="fas fa-bullseye"></i>
                        </button>
                    </div>

                    <!-- Pomodoro Timer -->
                    <button id="pomodoro-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" title="Pomodoro Timer">
                        <i class="fas fa-clock text-gray-600 dark:text-gray-400"></i>
                    </button>

                    <!-- AI Settings -->
                    <button id="ai-settings-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" title="AI Settings">
                        <i class="fas fa-brain text-gray-600 dark:text-gray-400"></i>
                    </button>

                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                        <i class="fas fa-moon dark:hidden text-gray-600"></i>
                        <i class="fas fa-sun hidden dark:block text-yellow-400"></i>
                    </button>
                </div>
            </div>
            
            <!-- Stats -->
            <div class="mt-4 grid grid-cols-3 gap-4">
                <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="total-tasks">0</div>
                    <div class="text-sm text-blue-600 dark:text-blue-400">Total Tasks</div>
                </div>
                <div class="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="completed-tasks">0</div>
                    <div class="text-sm text-green-600 dark:text-green-400">Completed</div>
                </div>
                <div class="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg text-center">
                    <div class="text-2xl font-bold text-orange-600 dark:text-orange-400" id="pending-tasks">0</div>
                    <div class="text-sm text-orange-600 dark:text-orange-400">Pending</div>
                </div>
            </div>
        </div>
    </header>

    <!-- Analytics Dashboard -->
    <div id="analytics-dashboard" class="hidden max-w-6xl mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
            <!-- Productivity Chart -->
            <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Productivity Trends</h3>
                <canvas id="productivity-chart" width="400" height="200"></canvas>
            </div>

            <!-- Task Distribution -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Task Distribution</h3>
                <canvas id="distribution-chart" width="300" height="300"></canvas>
            </div>

            <!-- Time Tracking -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Time Tracking</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">Today</span>
                        <span class="font-semibold text-gray-900 dark:text-white" id="time-today">0h 0m</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">This Week</span>
                        <span class="font-semibold text-gray-900 dark:text-white" id="time-week">0h 0m</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">Average/Day</span>
                        <span class="font-semibold text-gray-900 dark:text-white" id="time-average">0h 0m</span>
                    </div>
                </div>
            </div>

            <!-- Productivity Score -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Productivity Score</h3>
                <div class="text-center">
                    <div class="text-4xl font-bold text-primary-500 mb-2" id="productivity-score">85</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Based on completion rate and consistency</div>
                    <div class="mt-4 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div class="bg-primary-500 h-2 rounded-full transition-all duration-500" id="productivity-bar" style="width: 85%"></div>
                    </div>
                </div>
            </div>

            <!-- Streak Counter -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Current Streak</h3>
                <div class="text-center">
                    <div class="text-4xl font-bold text-orange-500 mb-2" id="streak-count">7</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Days with completed tasks</div>
                    <div class="mt-4 flex justify-center space-x-1">
                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Task Completion Timeline -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Completion Timeline</h3>
                <canvas id="timeline-chart" width="400" height="200"></canvas>
            </div>

            <!-- Category Performance -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Category Performance</h3>
                <div class="space-y-3" id="category-performance">
                    <!-- Dynamic content will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Pomodoro Timer Modal -->
    <div id="pomodoro-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 max-w-md mx-4 animate-bounce-in">
            <div class="text-center">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Pomodoro Timer</h3>

                <!-- Timer Display -->
                <div class="text-6xl font-mono font-bold text-primary-500 mb-6" id="pomodoro-display">25:00</div>

                <!-- Timer Controls -->
                <div class="flex justify-center space-x-4 mb-6">
                    <button id="pomodoro-start" class="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-play mr-2"></i>Start
                    </button>
                    <button id="pomodoro-pause" class="px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors">
                        <i class="fas fa-pause mr-2"></i>Pause
                    </button>
                    <button id="pomodoro-reset" class="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-stop mr-2"></i>Reset
                    </button>
                </div>

                <!-- Timer Settings -->
                <div class="grid grid-cols-3 gap-2 mb-6">
                    <button class="pomodoro-preset px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-sm" data-minutes="25">25m Work</button>
                    <button class="pomodoro-preset px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-sm" data-minutes="5">5m Break</button>
                    <button class="pomodoro-preset px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-sm" data-minutes="15">15m Break</button>
                </div>

                <!-- Close Button -->
                <button id="close-pomodoro" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Habit Tracker -->
    <div id="habit-tracker" class="hidden max-w-4xl mx-auto px-4 py-8">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Habit Tracker</h2>
                <button id="add-habit" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Add Habit
                </button>
            </div>

            <div id="habits-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Habits will be dynamically inserted here -->
            </div>
        </div>
    </div>

    <!-- Goal Setting -->
    <div id="goal-setting" class="hidden max-w-4xl mx-auto px-4 py-8">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8 border border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">Goal Setting & Tracking</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Weekly Goals -->
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-3">Weekly Goals</h3>
                    <div id="weekly-goals" class="space-y-2">
                        <!-- Weekly goals will be inserted here -->
                    </div>
                    <button onclick="todoApp.addGoal('weekly')" class="mt-3 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                        <i class="fas fa-plus mr-1"></i>Add Weekly Goal
                    </button>
                </div>

                <!-- Monthly Goals -->
                <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-purple-800 dark:text-purple-300 mb-3">Monthly Goals</h3>
                    <div id="monthly-goals" class="space-y-2">
                        <!-- Monthly goals will be inserted here -->
                    </div>
                    <button onclick="todoApp.addGoal('monthly')" class="mt-3 text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200">
                        <i class="fas fa-plus mr-1"></i>Add Monthly Goal
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 py-8" id="main-content">
        <!-- AI Suggestions -->
        <div id="ai-suggestions" class="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-xl shadow-lg p-6 mb-8 border border-purple-200 dark:border-purple-700">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-brain text-white text-sm"></i>
                    </div>
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">AI Suggestions</h2>
                </div>
                <button id="refresh-suggestions" class="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
            <div id="suggestions-container" class="space-y-3">
                <!-- AI suggestions will be dynamically inserted here -->
            </div>
        </div>

        <!-- Add Task Form -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Add New Task</h2>
                <div class="flex items-center space-x-2">
                    <button id="ai-assist" class="px-3 py-1 text-sm bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-lg hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors">
                        <i class="fas fa-magic mr-1"></i>AI Assist
                    </button>
                    <button id="voice-input" class="px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors">
                        <i class="fas fa-microphone mr-1"></i>Voice
                    </button>
                </div>
            </div>
            <form id="add-task-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="task-title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Task Title</label>
                        <div class="relative">
                            <input type="text" id="task-title" required maxlength="100"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                   placeholder="Enter task title..." aria-describedby="title-help">
                            <div id="smart-suggestions" class="absolute top-full left-0 right-0 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg mt-1 shadow-lg z-10 hidden max-h-40 overflow-y-auto">
                                <!-- Smart suggestions will appear here -->
                            </div>
                        </div>
                        <div id="title-help" class="text-xs text-gray-500 mt-1">Maximum 100 characters</div>
                    </div>
                    <div>
                        <label for="task-category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
                        <select id="task-category"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="personal">Personal</option>
                            <option value="work">Work</option>
                            <option value="shopping">Shopping</option>
                            <option value="health">Health</option>
                            <option value="learning">Learning</option>
                            <option value="fitness">Fitness</option>
                            <option value="finance">Finance</option>
                            <option value="creative">Creative</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="task-priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priority</label>
                        <select id="task-priority"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="low">🟢 Low</option>
                            <option value="medium" selected>🟡 Medium</option>
                            <option value="high">🔴 High</option>
                            <option value="urgent">🚨 Urgent</option>
                        </select>
                    </div>
                    <div>
                        <label for="task-due-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Due Date</label>
                        <input type="date" id="task-due-date"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    <div>
                        <label for="task-estimated-time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estimated Time</label>
                        <select id="task-estimated-time"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="">Not specified</option>
                            <option value="15">15 minutes</option>
                            <option value="30">30 minutes</option>
                            <option value="60">1 hour</option>
                            <option value="120">2 hours</option>
                            <option value="240">4 hours</option>
                            <option value="480">8 hours</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="task-tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tags</label>
                        <input type="text" id="task-tags"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                               placeholder="Enter tags separated by commas...">
                        <div class="text-xs text-gray-500 mt-1">e.g., urgent, meeting, review</div>
                    </div>
                    <div>
                        <label for="task-assignee" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Assign To</label>
                        <input type="email" id="task-assignee"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                               placeholder="Enter email address...">
                        <div class="text-xs text-gray-500 mt-1">Leave empty to assign to yourself</div>
                    </div>
                </div>
                
                <div>
                    <label for="task-description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description (Optional)</label>
                    <textarea id="task-description" rows="2" maxlength="500"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                              placeholder="Add task description..." aria-describedby="description-help"></textarea>
                    <div id="description-help" class="text-xs text-gray-500 mt-1">Maximum 500 characters (optional)</div>
                </div>
                
                <div class="flex space-x-3">
                    <button type="submit"
                            class="flex-1 bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2">
                        <i class="fas fa-plus"></i>
                        <span>Add Task</span>
                    </button>
                    <button type="button" onclick="todoApp.clearCompleted()"
                            class="px-4 py-2 bg-red-500 hover:bg-red-600 text-white font-medium rounded-lg transition-colors duration-200 flex items-center space-x-2">
                        <i class="fas fa-trash"></i>
                        <span class="hidden sm:inline">Clear Completed</span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8 border border-gray-200 dark:border-gray-700">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Advanced Search -->
                <div>
                    <label for="search-tasks" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Advanced Search</label>
                    <div class="relative">
                        <input type="text" id="search-tasks"
                               class="w-full pl-10 pr-20 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                               placeholder="Search by title, tags, or content...">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <button id="advanced-search-toggle" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>

                    <!-- Advanced Search Options -->
                    <div id="advanced-search-options" class="hidden mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Priority</label>
                                <select id="search-priority" class="w-full text-sm px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-600 text-gray-900 dark:text-white">
                                    <option value="">Any Priority</option>
                                    <option value="urgent">Urgent</option>
                                    <option value="high">High</option>
                                    <option value="medium">Medium</option>
                                    <option value="low">Low</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Due Date</label>
                                <select id="search-due-date" class="w-full text-sm px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-600 text-gray-900 dark:text-white">
                                    <option value="">Any Date</option>
                                    <option value="overdue">Overdue</option>
                                    <option value="today">Due Today</option>
                                    <option value="week">This Week</option>
                                    <option value="month">This Month</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filter by Status -->
                <div>
                    <label for="filter-status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Filter by Status</label>
                    <select id="filter-status" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="all">All Tasks</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                    </select>
                </div>
                
                <!-- Filter by Category -->
                <div>
                    <label for="filter-category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Filter by Category</label>
                    <select id="filter-category" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="all">All Categories</option>
                        <option value="personal">Personal</option>
                        <option value="work">Work</option>
                        <option value="shopping">Shopping</option>
                        <option value="health">Health</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Tasks List -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Your Tasks</h2>
                    <div class="flex items-center space-x-2">
                        <!-- Import Tasks -->
                        <label for="import-file" class="cursor-pointer px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors flex items-center space-x-1">
                            <i class="fas fa-upload"></i>
                            <span class="hidden sm:inline">Import</span>
                        </label>
                        <input type="file" id="import-file" accept=".json" class="hidden" onchange="if(this.files[0]) todoApp.importTasks(this.files[0])">

                        <!-- Export Tasks -->
                        <button onclick="todoApp.exportTasks()" class="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors flex items-center space-x-1">
                            <i class="fas fa-download"></i>
                            <span class="hidden sm:inline">Export</span>
                        </button>

                        <!-- Backup -->
                        <button onclick="todoApp.createBackup()" class="px-3 py-1 text-sm bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-lg hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors flex items-center space-x-1">
                            <i class="fas fa-shield-alt"></i>
                            <span class="hidden sm:inline">Backup</span>
                        </button>

                        <!-- Calendar Export -->
                        <button onclick="todoApp.exportToCalendar()" class="px-3 py-1 text-sm bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors flex items-center space-x-1">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="hidden sm:inline">Calendar</span>
                        </button>

                        <!-- AI Insights -->
                        <button onclick="todoApp.showAIInsights()" class="px-3 py-1 text-sm bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded-lg hover:bg-yellow-200 dark:hover:bg-yellow-900/50 transition-colors flex items-center space-x-1">
                            <i class="fas fa-lightbulb"></i>
                            <span class="hidden sm:inline">AI Insights</span>
                        </button>
                    </div>
                </div>
            </div>
            <div id="tasks-container" class="p-6">
                <!-- Tasks will be dynamically inserted here -->
                <div id="empty-state" class="text-center py-12">
                    <i class="fas fa-clipboard-list text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                    <h3 class="text-xl font-medium text-gray-500 dark:text-gray-400 mb-2">No tasks yet</h3>
                    <p class="text-gray-400 dark:text-gray-500">Add your first task to get started!</p>
                </div>
            </div>
        </div>
    </main>

    <!-- AI Settings Modal -->
    <div id="ai-settings-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md mx-4 animate-bounce-in">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-brain text-purple-500 mr-2"></i>
                    AI Settings
                </h3>
                <button id="close-ai-settings" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- API Key Configuration -->
            <div class="mb-6">
                <label for="api-key-input" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    OpenRouter API Key
                </label>
                <div class="relative">
                    <input type="password" id="api-key-input"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10"
                           placeholder="sk-or-v1-...">
                    <button id="toggle-api-key" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Get your free API key from <a href="https://openrouter.ai" target="_blank" class="text-purple-500 hover:text-purple-600">OpenRouter.ai</a>
                </p>
            </div>

            <!-- Model Selection -->
            <div class="mb-6">
                <label for="ai-model-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    AI Model
                </label>
                <select id="ai-model-select"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <!-- Options will be populated by JavaScript -->
                </select>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Free models available with OpenRouter account
                </p>
            </div>

            <!-- AI Features Toggle -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-3">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">AI Features</label>
                    <button id="ai-enabled-toggle" class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 dark:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                        <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"></span>
                    </button>
                </div>

                <div class="space-y-2 text-sm">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Smart Categorization</span>
                        <input type="checkbox" id="ai-categorization" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Task Suggestions</span>
                        <input type="checkbox" id="ai-suggestions" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Smart Prioritization</span>
                        <input type="checkbox" id="ai-prioritization" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Analytics Insights</span>
                        <input type="checkbox" id="ai-analytics" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                    </div>
                </div>
            </div>

            <!-- Privacy Settings -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Data Anonymization</label>
                    <input type="checkbox" id="data-anonymization" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500" checked>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Remove personal information before sending to AI
                </p>
            </div>

            <!-- Privacy Notice -->
            <div class="mb-6 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                <h4 class="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">Privacy Notice</h4>
                <p class="text-xs text-blue-700 dark:text-blue-400 leading-relaxed">
                    When AI features are enabled, task data may be sent to OpenRouter/AI providers for processing.
                    Data is anonymized when possible and not stored by AI providers.
                    <a href="#" onclick="todoApp.showPrivacyDetails()" class="underline hover:no-underline">Learn more</a>
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-3">
                <button id="test-ai-connection" class="flex-1 px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                    Test Connection
                </button>
                <button id="save-ai-settings" class="flex-1 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                    Save Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md mx-4 animate-bounce-in">
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Delete Task</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">Are you sure you want to delete this task? This action cannot be undone.</p>
                <div class="flex space-x-3">
                    <button id="cancel-delete" class="flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                        Cancel
                    </button>
                    <button id="confirm-delete" class="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                        Delete
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="ai-service.js"></script>
    <script src="debug-ai-service.js"></script>
    <script src="app.js"></script>
    <script src="ai-integration-test.js"></script>
</body>
</html>
