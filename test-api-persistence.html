<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Key Persistence Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 300px; }
        #results { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>API Key Persistence Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Manual API Key Test</h2>
        <p>Enter a test API key and verify it persists after refresh:</p>
        <input type="text" id="test-api-key" placeholder="sk-or-v1-your-test-key" />
        <button onclick="storeTestKey()">Store Test Key</button>
        <button onclick="checkStoredKey()">Check Stored Key</button>
        <button onclick="clearTestKey()">Clear Test Key</button>
        <div id="manual-test-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Automated Persistence Test</h2>
        <button onclick="runAutomatedTest()">Run Automated Test</button>
        <div id="automated-test-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Integration with Todo App</h2>
        <button onclick="testTodoAppIntegration()">Test Todo App Integration</button>
        <div id="integration-test-result"></div>
    </div>

    <div id="results">
        <h3>Test Results:</h3>
        <div id="test-output"></div>
    </div>

    <script src="ai-service.js"></script>
    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('test-output');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            output.innerHTML += `<div class="${className}">${message}</div>`;
        }

        function storeTestKey() {
            const key = document.getElementById('test-api-key').value.trim();
            if (!key) {
                document.getElementById('manual-test-result').innerHTML = '<span class="error">Please enter a test key</span>';
                return;
            }
            
            localStorage.setItem('openrouter_api_key', key);
            document.getElementById('manual-test-result').innerHTML = '<span class="success">Test key stored! Now refresh the page and click "Check Stored Key"</span>';
            log(`Manual test: Stored key ${key}`, 'success');
        }

        function checkStoredKey() {
            const storedKey = localStorage.getItem('openrouter_api_key');
            const inputKey = document.getElementById('test-api-key').value.trim();
            
            if (storedKey) {
                document.getElementById('test-api-key').value = storedKey;
                document.getElementById('manual-test-result').innerHTML = `<span class="success">Stored key found: ${storedKey}</span>`;
                log(`Manual test: Retrieved key ${storedKey}`, 'success');
            } else {
                document.getElementById('manual-test-result').innerHTML = '<span class="error">No stored key found</span>';
                log('Manual test: No stored key found', 'error');
            }
        }

        function clearTestKey() {
            localStorage.removeItem('openrouter_api_key');
            document.getElementById('test-api-key').value = '';
            document.getElementById('manual-test-result').innerHTML = '<span class="info">Test key cleared</span>';
            log('Manual test: Key cleared', 'info');
        }

        function runAutomatedTest() {
            const output = document.getElementById('automated-test-result');
            output.innerHTML = '<div>Running automated test...</div>';
            
            const testKey = 'sk-or-v1-automated-test-' + Date.now();
            
            try {
                // Test 1: Store and retrieve
                localStorage.setItem('openrouter_api_key', testKey);
                const retrieved = localStorage.getItem('openrouter_api_key');
                
                if (retrieved === testKey) {
                    output.innerHTML += '<div class="success">✓ localStorage store/retrieve works</div>';
                    log('Automated test: localStorage works', 'success');
                } else {
                    output.innerHTML += '<div class="error">✗ localStorage store/retrieve failed</div>';
                    log('Automated test: localStorage failed', 'error');
                    return;
                }
                
                // Test 2: AI Service initialization
                if (window.AIService) {
                    const aiService = new window.AIService();
                    if (aiService.apiKey === testKey) {
                        output.innerHTML += '<div class="success">✓ AI Service retrieves stored key</div>';
                        log('Automated test: AI Service key retrieval works', 'success');
                    } else {
                        output.innerHTML += '<div class="error">✗ AI Service key retrieval failed</div>';
                        log(`Automated test: AI Service key retrieval failed. Expected: ${testKey}, Got: ${aiService.apiKey}`, 'error');
                    }
                } else {
                    output.innerHTML += '<div class="error">✗ AI Service not available</div>';
                    log('Automated test: AI Service not available', 'error');
                }
                
                // Clean up
                localStorage.removeItem('openrouter_api_key');
                output.innerHTML += '<div class="info">Test completed and cleaned up</div>';
                log('Automated test: Completed and cleaned up', 'info');
                
            } catch (error) {
                output.innerHTML += `<div class="error">✗ Test failed: ${error.message}</div>`;
                log(`Automated test failed: ${error.message}`, 'error');
            }
        }

        function testTodoAppIntegration() {
            const output = document.getElementById('integration-test-result');
            output.innerHTML = '<div>Testing Todo App integration...</div>';
            
            // Check if we're in the todo app context
            if (window.location.pathname.includes('index.html') || window.todoApp) {
                output.innerHTML += '<div class="success">✓ Running in Todo App context</div>';
                
                if (window.todoApp && window.todoApp.aiService) {
                    const currentKey = window.todoApp.aiService.apiKey;
                    output.innerHTML += `<div class="info">Current AI Service key: ${currentKey}</div>`;
                    
                    // Check if loadAISettings populates the input field
                    const apiKeyInput = document.getElementById('api-key-input');
                    if (apiKeyInput) {
                        output.innerHTML += '<div class="success">✓ API key input field found</div>';
                        
                        if (currentKey && currentKey !== 'sk-or-v1-demo-key' && apiKeyInput.value === currentKey) {
                            output.innerHTML += '<div class="success">✓ API key properly loaded into input field</div>';
                            log('Integration test: API key properly loaded into UI', 'success');
                        } else {
                            output.innerHTML += '<div class="error">✗ API key not loaded into input field</div>';
                            log('Integration test: API key not loaded into UI', 'error');
                        }
                    } else {
                        output.innerHTML += '<div class="error">✗ API key input field not found</div>';
                        log('Integration test: API key input field not found', 'error');
                    }
                } else {
                    output.innerHTML += '<div class="error">✗ Todo App AI service not available</div>';
                    log('Integration test: Todo App AI service not available', 'error');
                }
            } else {
                output.innerHTML += '<div class="info">Not in Todo App context - open this test from the main app</div>';
                log('Integration test: Not in Todo App context', 'info');
            }
        }

        // Run initial check on page load
        window.addEventListener('load', () => {
            log('=== API Key Persistence Test Started ===', 'info');
            checkStoredKey();
        });
    </script>
</body>
</html>
