// Comprehensive AI Service Debugger
class AIServiceDebugger {
    constructor() {
        this.logs = [];
        this.startTime = Date.now();
    }

    log(message, data = null) {
        const timestamp = Date.now() - this.startTime;
        const logEntry = {
            timestamp,
            message,
            data: data ? JSON.parse(JSON.stringify(data)) : null
        };
        this.logs.push(logEntry);
        console.log(`[${timestamp}ms] ${message}`, data || '');
    }

    debugAIService() {
        this.log('=== AI SERVICE DEBUG START ===');
        
        // Check if AI Service class exists
        if (!window.AIService) {
            this.log('❌ CRITICAL: AIService class not found');
            return;
        }
        this.log('✅ AIService class found');

        // Create AI Service instance
        let aiService;
        try {
            aiService = new window.AIService();
            this.log('✅ AIService instance created');
        } catch (error) {
            this.log('❌ CRITICAL: Failed to create AIService instance', error);
            return;
        }

        // Check localStorage directly
        const storedKey = localStorage.getItem('openrouter_api_key');
        this.log('📦 localStorage API key', storedKey);

        // Check AI Service properties
        this.log('🔧 AI Service Properties:', {
            apiKey: aiService.apiKey,
            aiEnabled: aiService.aiEnabled,
            baseURL: aiService.baseURL,
            defaultModel: aiService.defaultModel
        });

        // Test API key validation
        if (aiService.apiKey) {
            const validation = aiService.validateAPIKey(aiService.apiKey);
            this.log('🔍 API Key Validation:', validation);
        }

        // Test isReady method
        const isReady = aiService.isReady();
        this.log('🚀 isReady() result:', isReady);

        // Get detailed readiness status
        const status = aiService.getReadinessStatus();
        this.log('📊 Detailed Readiness Status:', status);

        // Check AI settings
        const settings = aiService.getSettings();
        this.log('⚙️ AI Settings:', settings);

        // Test if TodoApp exists and has AI service
        if (window.todoApp) {
            this.log('✅ TodoApp found');
            this.log('🔗 TodoApp AI Service:', {
                hasAIService: !!window.todoApp.aiService,
                aiServiceReady: window.todoApp.aiService ? window.todoApp.aiService.isReady() : false
            });
        } else {
            this.log('❌ TodoApp not found');
        }

        this.log('=== AI SERVICE DEBUG END ===');
        return this.logs;
    }

    testAPIKeyFlow() {
        this.log('=== API KEY FLOW TEST START ===');
        
        const testKey = 'sk-or-v1-test-debug-key-12345';
        
        try {
            // Store test key
            localStorage.setItem('openrouter_api_key', testKey);
            this.log('✅ Test key stored in localStorage');

            // Create new AI service (simulates page reload)
            const aiService = new window.AIService();
            this.log('✅ New AI service created');

            // Check if key was retrieved
            this.log('🔍 Retrieved API key:', aiService.apiKey);
            this.log('🔍 Keys match:', aiService.apiKey === testKey);

            // Test validation
            const validation = aiService.validateAPIKey(aiService.apiKey);
            this.log('🔍 Validation result:', validation);

            // Test readiness
            const isReady = aiService.isReady();
            this.log('🚀 isReady() with test key:', isReady);

            // Clean up
            localStorage.removeItem('openrouter_api_key');
            this.log('🧹 Test key cleaned up');

        } catch (error) {
            this.log('❌ Error in API key flow test:', error);
        }

        this.log('=== API KEY FLOW TEST END ===');
    }

    testSuggestionFlow() {
        this.log('=== SUGGESTION FLOW TEST START ===');
        
        if (!window.todoApp || !window.todoApp.aiService) {
            this.log('❌ TodoApp or AI service not available');
            return;
        }

        const aiService = window.todoApp.aiService;
        
        // Test the exact logic used in generateInitialAISuggestions
        this.log('🔍 Testing suggestion logic...');
        this.log('aiService exists:', !!aiService);
        this.log('aiService.isReady():', aiService.isReady());
        
        const shouldUseRealAI = aiService && aiService.isReady();
        this.log('🎯 Should use real AI:', shouldUseRealAI);
        
        if (shouldUseRealAI) {
            this.log('✅ Logic says: Use real AI');
        } else {
            this.log('🎭 Logic says: Use mock AI');
            
            // Debug why it's not ready
            if (!aiService) {
                this.log('❌ Reason: No AI service');
            } else if (!aiService.isReady()) {
                this.log('❌ Reason: AI service not ready');
                this.log('🔍 Readiness details:', aiService.getReadinessStatus());
            }
        }

        this.log('=== SUGGESTION FLOW TEST END ===');
    }

    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            logs: this.logs,
            summary: this.generateSummary()
        };
        
        console.log('=== COMPLETE DEBUG REPORT ===');
        console.log(report);
        
        return report;
    }

    generateSummary() {
        const errors = this.logs.filter(log => log.message.includes('❌'));
        const successes = this.logs.filter(log => log.message.includes('✅'));
        
        return {
            totalLogs: this.logs.length,
            errors: errors.length,
            successes: successes.length,
            criticalIssues: errors.filter(log => log.message.includes('CRITICAL')),
            recommendations: this.generateRecommendations()
        };
    }

    generateRecommendations() {
        const recommendations = [];
        
        // Check for common issues
        const hasAPIKey = this.logs.some(log => 
            log.message.includes('localStorage API key') && log.data && log.data !== 'null'
        );
        
        if (!hasAPIKey) {
            recommendations.push('No API key found in localStorage - user needs to enter API key');
        }

        const hasValidation = this.logs.find(log => 
            log.message.includes('API Key Validation')
        );
        
        if (hasValidation && !hasValidation.data?.isValid) {
            recommendations.push('API key validation failed - check key format');
        }

        const isReady = this.logs.find(log => 
            log.message.includes('isReady() result')
        );
        
        if (isReady && !isReady.data) {
            recommendations.push('AI service not ready - check API key and settings');
        }

        return recommendations;
    }
}

// Auto-run debugger when script loads
window.aiDebugger = new AIServiceDebugger();

// Add global debug functions
window.debugAI = () => {
    return window.aiDebugger.debugAIService();
};

window.testAPIFlow = () => {
    return window.aiDebugger.testAPIKeyFlow();
};

window.testSuggestionFlow = () => {
    return window.aiDebugger.testSuggestionFlow();
};

window.getAIReport = () => {
    return window.aiDebugger.generateReport();
};

console.log('🔧 AI Service Debugger loaded. Use debugAI(), testAPIFlow(), testSuggestionFlow(), or getAIReport() in console.');
