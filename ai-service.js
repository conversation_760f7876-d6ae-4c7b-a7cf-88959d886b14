// AI Service for OpenRouter Integration
class AIService {
    constructor() {
        this.baseURL = 'https://openrouter.ai/api/v1';
        this.apiKey = this.getAPIKey();
        this.defaultModel = 'deepseek/deepseek-r1';
        this.fallbackModel = 'deepseek/deepseek-v3';
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.cache = new Map();
        this.rateLimitDelay = 1000; // 1 second between requests
        this.lastRequestTime = 0;
        this.maxRetries = 3;
        this.requestTimeout = 30000; // 30 seconds
        
        // Available free models
        this.freeModels = [
            { id: 'deepseek/deepseek-r1', name: 'DeepSeek R1', description: 'Advanced reasoning model' },
            { id: 'deepseek/deepseek-v3', name: 'DeepSeek V3', description: 'High-performance general model' },
            { id: 'qwen/qwen-2.5-7b-instruct', name: 'Qwen 2.5 7B', description: 'Efficient instruction-following model' },
            { id: 'meta-llama/llama-3.2-3b-instruct', name: 'Llama 3.2 3B', description: 'Meta\'s compact model' }
        ];
        
        this.initializeSettings();
    }

    // API Key Management
    getAPIKey() {
        // Try to get from localStorage first (user-provided)
        let apiKey = localStorage.getItem('openrouter_api_key');
        
        if (!apiKey) {
            // For demo purposes, we'll use a placeholder
            // In production, this should be handled server-side
            apiKey = 'sk-or-v1-demo-key'; // Placeholder for demo
        }
        
        return apiKey;
    }

    setAPIKey(key) {
        // Validate API key format
        const validationResult = this.validateAPIKey(key);
        if (!validationResult.isValid) {
            throw new Error(validationResult.error);
        }

        localStorage.setItem('openrouter_api_key', key);
        this.apiKey = key;
        this.showNotification('API key updated successfully', 'success');
    }

    validateAPIKey(key) {
        if (!key || key.trim() === '') {
            return {
                isValid: false,
                error: 'API key cannot be empty'
            };
        }

        const trimmedKey = key.trim();

        if (trimmedKey === 'sk-or-v1-demo-key') {
            return {
                isValid: false,
                error: 'Please enter your actual OpenRouter API key, not the demo placeholder'
            };
        }

        if (!trimmedKey.startsWith('sk-or-v1-')) {
            return {
                isValid: false,
                error: 'OpenRouter API keys must start with "sk-or-v1-". Please check your key format.'
            };
        }

        if (trimmedKey.length < 20) {
            return {
                isValid: false,
                error: 'API key appears to be too short. Please check that you copied the complete key.'
            };
        }

        return {
            isValid: true,
            error: null
        };
    }

    // Settings Management
    initializeSettings() {
        const settings = this.getSettings();
        this.defaultModel = settings.model || this.defaultModel;
        this.aiEnabled = settings.enabled !== false; // Default to true
        this.dataAnonymization = settings.anonymization !== false; // Default to true
    }

    getSettings() {
        const saved = localStorage.getItem('ai_settings');
        return saved ? JSON.parse(saved) : {};
    }

    updateSettings(newSettings) {
        const currentSettings = this.getSettings();
        const updatedSettings = { ...currentSettings, ...newSettings };
        localStorage.setItem('ai_settings', JSON.stringify(updatedSettings));
        this.initializeSettings();
    }

    // Request Management
    async makeRequest(prompt, options = {}) {
        if (!this.aiEnabled) {
            throw new Error('AI features are disabled');
        }

        if (!this.apiKey || this.apiKey === 'sk-or-v1-demo-key') {
            throw new Error('OpenRouter API key not configured');
        }

        const cacheKey = this.generateCacheKey(prompt, options);
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        // Add to queue for rate limiting
        return new Promise((resolve, reject) => {
            this.requestQueue.push({
                prompt,
                options,
                resolve,
                reject,
                cacheKey,
                retries: 0
            });
            
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();
            
            try {
                // Rate limiting
                const timeSinceLastRequest = Date.now() - this.lastRequestTime;
                if (timeSinceLastRequest < this.rateLimitDelay) {
                    await this.sleep(this.rateLimitDelay - timeSinceLastRequest);
                }

                const response = await this.executeRequest(request);
                
                // Cache successful response
                this.cache.set(request.cacheKey, response);
                
                // Limit cache size
                if (this.cache.size > 100) {
                    const firstKey = this.cache.keys().next().value;
                    this.cache.delete(firstKey);
                }
                
                request.resolve(response);
                this.lastRequestTime = Date.now();
                
            } catch (error) {
                if (request.retries < this.maxRetries) {
                    request.retries++;
                    this.requestQueue.unshift(request); // Retry at front of queue
                    await this.sleep(1000 * Math.pow(2, request.retries)); // Exponential backoff
                } else {
                    request.reject(error);
                }
            }
        }

        this.isProcessingQueue = false;
    }

    async executeRequest(request) {
        const { prompt, options } = request;
        const model = options.model || this.defaultModel;
        
        const requestBody = {
            model: model,
            messages: [
                {
                    role: 'system',
                    content: options.systemPrompt || 'You are a helpful AI assistant for a task management application. Provide concise, practical responses.'
                },
                {
                    role: 'user',
                    content: this.dataAnonymization ? this.anonymizeData(prompt) : prompt
                }
            ],
            max_tokens: options.maxTokens || 500,
            temperature: options.temperature || 0.7,
            top_p: options.topP || 0.9
        };

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

        try {
            const response = await fetch(`${this.baseURL}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': window.location.origin,
                    'X-Title': 'Advanced Todo App'
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API request failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
            }

            const data = await response.json();
            
            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('Invalid response format from AI service');
            }

            return {
                content: data.choices[0].message.content,
                model: data.model,
                usage: data.usage,
                timestamp: Date.now()
            };

        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('Request timeout - AI service took too long to respond');
            }
            
            throw error;
        }
    }

    // Utility Methods
    generateCacheKey(prompt, options) {
        const key = JSON.stringify({ prompt, model: options.model || this.defaultModel });
        return btoa(key).substring(0, 32); // Simple hash
    }

    anonymizeData(text) {
        if (!this.dataAnonymization) return text;
        
        // Basic anonymization - replace potential personal info
        return text
            .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]')
            .replace(/\b\d{3}-\d{3}-\d{4}\b/g, '[PHONE]')
            .replace(/\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/g, '[CARD]');
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    showNotification(message, type) {
        // Integration with existing notification system
        if (window.todoApp && window.todoApp.showNotification) {
            window.todoApp.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    // Health Check
    async testConnection() {
        try {
            const response = await this.makeRequest('Hello, please respond with "AI connection successful"', {
                maxTokens: 50,
                temperature: 0
            });
            
            return response.content.includes('successful');
        } catch (error) {
            console.error('AI connection test failed:', error);
            return false;
        }
    }

    // Model Management
    getAvailableModels() {
        return this.freeModels;
    }

    setModel(modelId) {
        const model = this.freeModels.find(m => m.id === modelId);
        if (!model) {
            throw new Error('Invalid model ID');
        }
        
        this.defaultModel = modelId;
        this.updateSettings({ model: modelId });
        this.showNotification(`Switched to ${model.name}`, 'success');
    }

    // Error Handling
    handleError(error, context = '') {
        console.error(`AI Service Error ${context}:`, error);
        
        let userMessage = 'AI service temporarily unavailable';
        
        if (error.message.includes('API key')) {
            userMessage = 'Please configure your OpenRouter API key in settings';
        } else if (error.message.includes('timeout')) {
            userMessage = 'AI request timed out, please try again';
        } else if (error.message.includes('rate limit')) {
            userMessage = 'Too many requests, please wait a moment';
        }
        
        this.showNotification(userMessage, 'error');
        throw new Error(userMessage);
    }

    // Performance Optimization Methods
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Batch multiple requests together
    batchRequests(requests, batchSize = 3) {
        const batches = [];
        for (let i = 0; i < requests.length; i += batchSize) {
            batches.push(requests.slice(i, i + batchSize));
        }
        return batches;
    }

    // Background processing for non-critical AI features
    async processInBackground(aiFunction, fallback) {
        if (window.requestIdleCallback) {
            return new Promise((resolve) => {
                window.requestIdleCallback(async () => {
                    try {
                        const result = await aiFunction();
                        resolve(result);
                    } catch (error) {
                        resolve(fallback());
                    }
                });
            });
        } else {
            return new Promise((resolve) => {
                setTimeout(async () => {
                    try {
                        const result = await aiFunction();
                        resolve(result);
                    } catch (error) {
                        resolve(fallback());
                    }
                }, 100);
            });
        }
    }

    // Smart caching with TTL
    setCacheWithTTL(key, value, ttlMinutes = 30) {
        const expiry = Date.now() + (ttlMinutes * 60 * 1000);
        this.cache.set(key, { value, expiry });
    }

    getCacheWithTTL(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;

        if (Date.now() > cached.expiry) {
            this.cache.delete(key);
            return null;
        }

        return cached.value;
    }
}

// Export for use in main application
window.AIService = AIService;
