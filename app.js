// Advanced Todo List Application with AI and Analytics
class TodoApp {
    constructor() {
        this.tasks = this.loadTasks();
        this.currentFilter = { status: 'all', category: 'all', search: '' };
        this.draggedTask = null;
        this.taskToDelete = null;
        this.analytics = this.loadAnalytics();
        this.pomodoro = { timer: null, timeLeft: 1500, isRunning: false, mode: 'work' };
        this.aiSuggestions = [];
        this.currentView = 'tasks';
        this.speechRecognition = null;
        this.charts = {};
        this.habits = this.loadHabits();
        this.goals = this.loadGoals();
        this.aiService = null;
        this.aiLoadingStates = new Set();

        this.initializeEventListeners();
        this.initializeTheme();
        this.initializeAnalytics();
        this.initializeAI();
        this.initializeSpeechRecognition();
        this.initializeAIService();
        this.renderTasks();
        this.updateStats();
        this.generateInitialAISuggestions();
        this.requestNotificationPermission();
        this.setupAutomationRules();

        // Check AI consent after initialization
        setTimeout(() => this.checkAIConsent(), 2000);
    }

    // Initialize all event listeners
    initializeEventListeners() {
        // Theme toggle
        document.getElementById('theme-toggle').addEventListener('click', this.toggleTheme.bind(this));

        // Add task form
        document.getElementById('add-task-form').addEventListener('submit', this.addTask.bind(this));

        // Search and filters
        document.getElementById('search-tasks').addEventListener('input', this.handleSearch.bind(this));
        document.getElementById('filter-status').addEventListener('change', this.handleStatusFilter.bind(this));
        document.getElementById('filter-category').addEventListener('change', this.handleCategoryFilter.bind(this));

        // Delete modal
        document.getElementById('cancel-delete').addEventListener('click', this.hideDeleteModal.bind(this));
        document.getElementById('confirm-delete').addEventListener('click', this.confirmDelete.bind(this));

        // Close modal on outside click
        document.getElementById('delete-modal').addEventListener('click', (e) => {
            if (e.target.id === 'delete-modal') {
                this.hideDeleteModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));

        // View navigation
        document.getElementById('tasks-view').addEventListener('click', () => this.switchView('tasks'));
        document.getElementById('analytics-view').addEventListener('click', () => this.switchView('analytics'));
        document.getElementById('habits-view').addEventListener('click', () => this.switchView('habits'));
        document.getElementById('goals-view').addEventListener('click', () => this.switchView('goals'));

        // Pomodoro timer
        document.getElementById('pomodoro-toggle').addEventListener('click', this.showPomodoroModal.bind(this));
        document.getElementById('close-pomodoro').addEventListener('click', this.hidePomodoroModal.bind(this));
        document.getElementById('pomodoro-start').addEventListener('click', this.startPomodoro.bind(this));
        document.getElementById('pomodoro-pause').addEventListener('click', this.pausePomodoro.bind(this));
        document.getElementById('pomodoro-reset').addEventListener('click', this.resetPomodoro.bind(this));

        // Pomodoro presets
        document.querySelectorAll('.pomodoro-preset').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const minutes = parseInt(e.target.dataset.minutes);
                this.setPomodoroTime(minutes);
            });
        });

        // AI features
        document.getElementById('ai-assist').addEventListener('click', this.showAIAssist.bind(this));
        document.getElementById('voice-input').addEventListener('click', this.toggleVoiceInput.bind(this));
        document.getElementById('refresh-suggestions').addEventListener('click', this.generateInitialAISuggestions.bind(this));

        // Smart suggestions and advanced search
        document.getElementById('task-title').addEventListener('input', this.handleSmartSuggestions.bind(this));
        document.getElementById('advanced-search-toggle').addEventListener('click', this.toggleAdvancedSearch.bind(this));
        document.getElementById('search-priority').addEventListener('change', this.handleAdvancedSearch.bind(this));
        document.getElementById('search-due-date').addEventListener('change', this.handleAdvancedSearch.bind(this));

        // Habit tracking
        document.getElementById('add-habit').addEventListener('click', this.addHabit.bind(this));

        // AI Settings
        document.getElementById('ai-settings-toggle').addEventListener('click', this.showAISettings.bind(this));
        document.getElementById('close-ai-settings').addEventListener('click', this.hideAISettings.bind(this));
        document.getElementById('toggle-api-key').addEventListener('click', this.toggleAPIKeyVisibility.bind(this));
        document.getElementById('test-ai-connection').addEventListener('click', this.testAIConnection.bind(this));
        document.getElementById('save-ai-settings').addEventListener('click', this.saveAISettings.bind(this));

        // Auto-save on window unload
        window.addEventListener('beforeunload', () => {
            this.saveTasks();
            this.saveAnalytics();
        });

        // Handle online/offline status
        window.addEventListener('online', () => {
            this.showNotification('You are back online!', 'success');
        });

        window.addEventListener('offline', () => {
            this.showNotification('You are offline. Changes will be saved locally.', 'info');
        });
    }

    // Theme management
    initializeTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
        }
    }

    toggleTheme() {
        document.documentElement.classList.toggle('dark');
        const isDark = document.documentElement.classList.contains('dark');
        localStorage.setItem('theme', isDark ? 'dark' : 'light');
    }

    // Analytics initialization
    initializeAnalytics() {
        this.updateAnalyticsDashboard();
        this.initializeCharts();
    }

    initializeCharts() {
        // Productivity Chart
        const productivityCtx = document.getElementById('productivity-chart');
        if (productivityCtx) {
            this.charts.productivity = new Chart(productivityCtx, {
                type: 'line',
                data: {
                    labels: this.getLast7Days(),
                    datasets: [{
                        label: 'Tasks Completed',
                        data: this.getProductivityData(),
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { display: false } },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
        }

        // Distribution Chart
        const distributionCtx = document.getElementById('distribution-chart');
        if (distributionCtx) {
            this.charts.distribution = new Chart(distributionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Personal', 'Work', 'Shopping', 'Health', 'Learning', 'Other'],
                    datasets: [{
                        data: this.getCategoryDistribution(),
                        backgroundColor: [
                            '#ef4444', '#f59e0b', '#10b981',
                            '#3b82f6', '#8b5cf6', '#6b7280'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { position: 'bottom' } }
                }
            });
        }

        // Timeline Chart
        const timelineCtx = document.getElementById('timeline-chart');
        if (timelineCtx) {
            this.charts.timeline = new Chart(timelineCtx, {
                type: 'bar',
                data: {
                    labels: this.getLast30Days(),
                    datasets: [{
                        label: 'Completed',
                        data: this.getTimelineData(),
                        backgroundColor: '#10b981'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { display: false } },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
        }
    }

    // AI initialization
    initializeAI() {
        this.aiPatterns = {
            work: ['meeting', 'email', 'report', 'presentation', 'call', 'review'],
            personal: ['grocery', 'exercise', 'read', 'clean', 'organize', 'plan'],
            health: ['doctor', 'medicine', 'workout', 'walk', 'meditation', 'sleep'],
            learning: ['study', 'course', 'book', 'tutorial', 'practice', 'research']
        };

        this.commonTasks = [
            'Check emails', 'Daily standup meeting', 'Review project status',
            'Buy groceries', 'Exercise for 30 minutes', 'Read for 20 minutes',
            'Plan tomorrow\'s tasks', 'Clean workspace', 'Take a walk',
            'Drink 8 glasses of water', 'Call family', 'Update project documentation'
        ];
    }

    // Speech recognition initialization
    initializeSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.speechRecognition = new SpeechRecognition();
            this.speechRecognition.continuous = false;
            this.speechRecognition.interimResults = false;
            this.speechRecognition.lang = 'en-US';

            this.speechRecognition.onresult = async (event) => {
                const transcript = event.results[0][0].transcript;

                // Use AI to parse natural language input
                if (this.aiService && this.aiService.aiEnabled) {
                    try {
                        await this.parseNaturalLanguageTask(transcript);
                    } catch (error) {
                        // Fallback to simple input
                        document.getElementById('task-title').value = transcript;
                        this.showNotification('Voice input captured!', 'success');
                    }
                } else {
                    document.getElementById('task-title').value = transcript;
                    this.showNotification('Voice input captured!', 'success');
                }
            };

            this.speechRecognition.onerror = () => {
                this.showNotification('Voice input failed. Please try again.', 'error');
            };
        }
    }

    // AI Service initialization
    initializeAIService() {
        if (window.AIService) {
            this.aiService = new window.AIService();
            this.populateAISettings();
        } else {
            console.warn('AI Service not available');
        }
    }

    populateAISettings() {
        if (!this.aiService) return;

        // Populate model dropdown
        const modelSelect = document.getElementById('ai-model-select');
        if (modelSelect) {
            modelSelect.innerHTML = this.aiService.getAvailableModels().map(model =>
                `<option value="${model.id}">${model.name} - ${model.description}</option>`
            ).join('');

            // Set current model
            const settings = this.aiService.getSettings();
            if (settings.model) {
                modelSelect.value = settings.model;
            }
        }

        // Set current settings
        this.loadAISettings();
    }

    loadAISettings() {
        if (!this.aiService) return;

        const settings = this.aiService.getSettings();

        // Load checkboxes
        document.getElementById('ai-categorization').checked = settings.categorization !== false;
        document.getElementById('ai-suggestions').checked = settings.suggestions !== false;
        document.getElementById('ai-prioritization').checked = settings.prioritization !== false;
        document.getElementById('ai-analytics').checked = settings.analytics !== false;
        document.getElementById('data-anonymization').checked = settings.anonymization !== false;

        // Load API key into input field
        const apiKeyInput = document.getElementById('api-key-input');
        if (apiKeyInput) {
            const storedApiKey = localStorage.getItem('openrouter_api_key');
            if (storedApiKey && storedApiKey !== 'sk-or-v1-demo-key') {
                apiKeyInput.value = storedApiKey;
            }
        }

        // Load AI enabled toggle
        const toggle = document.getElementById('ai-enabled-toggle');
        const toggleSpan = toggle.querySelector('span');
        if (settings.enabled !== false) {
            toggle.classList.add('bg-purple-600');
            toggle.classList.remove('bg-gray-200', 'dark:bg-gray-600');
            toggleSpan.classList.add('translate-x-5');
        }

        // Add toggle click handler
        toggle.addEventListener('click', () => {
            const isEnabled = toggle.classList.contains('bg-purple-600');

            if (isEnabled) {
                toggle.classList.remove('bg-purple-600');
                toggle.classList.add('bg-gray-200', 'dark:bg-gray-600');
                toggleSpan.classList.remove('translate-x-5');
            } else {
                toggle.classList.add('bg-purple-600');
                toggle.classList.remove('bg-gray-200', 'dark:bg-gray-600');
                toggleSpan.classList.add('translate-x-5');
            }
        });
    }

    // Task management
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    async addTask(e) {
        e.preventDefault();

        const title = document.getElementById('task-title').value.trim();
        const category = document.getElementById('task-category').value;
        const priority = document.getElementById('task-priority').value;
        const dueDate = document.getElementById('task-due-date').value;
        const description = document.getElementById('task-description').value.trim();
        const estimatedTime = document.getElementById('task-estimated-time').value;
        const tags = document.getElementById('task-tags').value.trim();
        const assignee = document.getElementById('task-assignee').value.trim();

        // Validation
        if (!title) {
            this.showNotification('Please enter a task title', 'error');
            document.getElementById('task-title').focus();
            return;
        }

        if (title.length > 100) {
            this.showNotification('Task title must be less than 100 characters', 'error');
            return;
        }

        if (description.length > 500) {
            this.showNotification('Task description must be less than 500 characters', 'error');
            return;
        }

        // Check for duplicate titles
        if (this.tasks.some(task => task.title.toLowerCase() === title.toLowerCase())) {
            this.showNotification('A task with this title already exists', 'error');
            return;
        }

        // Validate due date
        if (dueDate) {
            const selectedDate = new Date(dueDate);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate < today) {
                this.showNotification('Due date cannot be in the past', 'error');
                return;
            }
        }

        // AI-powered auto-categorization
        let finalCategory = category;
        if (category === 'other' && this.aiService && this.aiService.aiEnabled) {
            try {
                const aiCategory = await this.suggestCategoryWithAI(title, description);
                finalCategory = aiCategory || category;
            } catch (error) {
                // Fallback to pattern matching
                finalCategory = this.suggestCategory(title, description);
            }
        }

        let task = {
            id: this.generateId(),
            title,
            description,
            category: finalCategory,
            priority,
            dueDate,
            estimatedTime: estimatedTime ? parseInt(estimatedTime) : null,
            tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
            assignee: assignee || 'self',
            completed: false,
            createdAt: new Date().toISOString(),
            timeSpent: 0,
            comments: [],
            order: this.tasks.length,
            aiGenerated: false
        };

        // Apply automation rules
        task = this.applyAutomationRules(task);

        this.tasks.push(task);
        this.saveTasks();
        this.updateAnalytics('taskAdded', task);
        this.renderTasks();
        this.updateStats();
        this.updateAnalyticsDashboard();

        // Reset form
        document.getElementById('add-task-form').reset();
        this.showNotification('Task added successfully!', 'success');

        // Add animation to new task
        setTimeout(() => {
            const newTaskElement = document.querySelector(`[data-task-id="${task.id}"]`);
            if (newTaskElement) {
                newTaskElement.classList.add('animate-bounce-in');
            }
        }, 100);

        // Generate new AI suggestions
        if (this.aiService && this.aiService.aiEnabled) {
            this.generateAISuggestionsReal();
        } else {
            this.generateAISuggestions();
        }
    }

    toggleTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (task) {
            task.completed = !task.completed;
            task.completedAt = task.completed ? new Date().toISOString() : null;

            if (task.completed) {
                this.updateAnalytics('taskCompleted', task);
            }

            this.saveTasks();
            this.renderTasks();
            this.updateStats();
            this.updateAnalyticsDashboard();

            const message = task.completed ? 'Task completed!' : 'Task marked as active';
            this.showNotification(message, 'success');
        }
    }

    deleteTask(taskId) {
        this.taskToDelete = taskId;
        this.showDeleteModal();
    }

    confirmDelete() {
        if (this.taskToDelete) {
            this.tasks = this.tasks.filter(t => t.id !== this.taskToDelete);
            this.saveTasks();
            this.renderTasks();
            this.updateStats();
            this.hideDeleteModal();
            this.showNotification('Task deleted successfully', 'success');
            this.taskToDelete = null;
        }
    }

    editTask(taskId, field, value) {
        const task = this.tasks.find(t => t.id === taskId);
        if (task) {
            task[field] = value;
            task.updatedAt = new Date().toISOString();
            this.saveTasks();
            this.renderTasks();
            this.showNotification('Task updated successfully', 'success');
        }
    }

    // Filtering and searching
    handleSearch(e) {
        this.currentFilter.search = e.target.value.toLowerCase();
        this.renderTasks();
    }

    handleStatusFilter(e) {
        this.currentFilter.status = e.target.value;
        this.renderTasks();
    }

    handleCategoryFilter(e) {
        this.currentFilter.category = e.target.value;
        this.renderTasks();
    }

    getFilteredTasks() {
        return this.tasks.filter(task => {
            // Status filter
            if (this.currentFilter.status === 'active' && task.completed) return false;
            if (this.currentFilter.status === 'completed' && !task.completed) return false;

            // Category filter
            if (this.currentFilter.category !== 'all' && task.category !== this.currentFilter.category) return false;

            // Priority filter
            if (this.currentFilter.priority && task.priority !== this.currentFilter.priority) return false;

            // Due date filter
            if (this.currentFilter.dueDate) {
                const today = new Date();
                const taskDue = task.dueDate ? new Date(task.dueDate) : null;

                switch (this.currentFilter.dueDate) {
                    case 'overdue':
                        if (!taskDue || taskDue >= today || task.completed) return false;
                        break;
                    case 'today':
                        if (!taskDue || taskDue.toDateString() !== today.toDateString()) return false;
                        break;
                    case 'week':
                        const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
                        if (!taskDue || taskDue > weekFromNow) return false;
                        break;
                    case 'month':
                        const monthFromNow = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
                        if (!taskDue || taskDue > monthFromNow) return false;
                        break;
                }
            }

            // Search filter
            if (this.currentFilter.search) {
                const searchTerm = this.currentFilter.search;
                const searchText = [
                    task.title,
                    task.description,
                    task.category,
                    ...(task.tags || [])
                ].join(' ').toLowerCase();

                return searchText.includes(searchTerm);
            }

            return true;
        }).sort((a, b) => a.order - b.order);
    }

    // Drag and drop functionality
    handleDragStart(e, taskId) {
        this.draggedTask = taskId;
        e.target.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
    }

    handleDragEnd(e) {
        e.target.classList.remove('dragging');
        this.draggedTask = null;
        
        // Remove all drop zone indicators
        document.querySelectorAll('.drop-zone').forEach(el => {
            el.classList.remove('drop-zone');
        });
    }

    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        
        const taskElement = e.target.closest('.task-item');
        if (taskElement && !taskElement.classList.contains('dragging')) {
            taskElement.classList.add('drop-zone');
        }
    }

    handleDragLeave(e) {
        const taskElement = e.target.closest('.task-item');
        if (taskElement) {
            taskElement.classList.remove('drop-zone');
        }
    }

    handleDrop(e, targetTaskId) {
        e.preventDefault();
        
        if (this.draggedTask && this.draggedTask !== targetTaskId) {
            const draggedTaskIndex = this.tasks.findIndex(t => t.id === this.draggedTask);
            const targetTaskIndex = this.tasks.findIndex(t => t.id === targetTaskId);
            
            if (draggedTaskIndex !== -1 && targetTaskIndex !== -1) {
                // Reorder tasks
                const draggedTask = this.tasks[draggedTaskIndex];
                this.tasks.splice(draggedTaskIndex, 1);
                this.tasks.splice(targetTaskIndex, 0, draggedTask);
                
                // Update order property
                this.tasks.forEach((task, index) => {
                    task.order = index;
                });
                
                this.saveTasks();
                this.renderTasks();
                this.showNotification('Task order updated', 'success');
            }
        }
        
        // Remove drop zone indicator
        e.target.closest('.task-item')?.classList.remove('drop-zone');
    }

    // Utility functions
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        } else if (date.toDateString() === tomorrow.toDateString()) {
            return 'Tomorrow';
        } else {
            return date.toLocaleDateString();
        }
    }

    isOverdue(dueDate) {
        if (!dueDate) return false;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const due = new Date(dueDate);
        return due < today;
    }

    isDueToday(dueDate) {
        if (!dueDate) return false;
        const today = new Date();
        const due = new Date(dueDate);
        return today.toDateString() === due.toDateString();
    }

    getPriorityColor(priority) {
        const colors = {
            high: 'text-red-600 dark:text-red-400',
            medium: 'text-yellow-600 dark:text-yellow-400',
            low: 'text-green-600 dark:text-green-400'
        };
        return colors[priority] || colors.medium;
    }

    getCategoryIcon(category) {
        const icons = {
            personal: 'fas fa-user',
            work: 'fas fa-briefcase',
            shopping: 'fas fa-shopping-cart',
            health: 'fas fa-heart',
            other: 'fas fa-tag'
        };
        return icons[category] || icons.other;
    }

    // Data persistence
    saveTasks() {
        localStorage.setItem('todoTasks', JSON.stringify(this.tasks));
    }

    loadTasks() {
        const saved = localStorage.getItem('todoTasks');
        return saved ? JSON.parse(saved) : [];
    }

    // UI updates
    updateStats() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(t => t.completed).length;
        const pending = total - completed;
        
        document.getElementById('total-tasks').textContent = total;
        document.getElementById('completed-tasks').textContent = completed;
        document.getElementById('pending-tasks').textContent = pending;
    }

    showDeleteModal() {
        document.getElementById('delete-modal').classList.remove('hidden');
        document.getElementById('delete-modal').classList.add('flex');
    }

    hideDeleteModal() {
        document.getElementById('delete-modal').classList.add('hidden');
        document.getElementById('delete-modal').classList.remove('flex');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg animate-slide-in ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Render tasks
    renderTasks() {
        const container = document.getElementById('tasks-container');
        const emptyState = document.getElementById('empty-state');
        const filteredTasks = this.getFilteredTasks();

        if (filteredTasks.length === 0) {
            emptyState.style.display = 'block';
            // Clear any existing tasks
            const existingTasks = container.querySelectorAll('.task-item');
            existingTasks.forEach(task => task.remove());
            return;
        }

        emptyState.style.display = 'none';

        // Clear existing tasks
        const existingTasks = container.querySelectorAll('.task-item');
        existingTasks.forEach(task => task.remove());

        // Render filtered tasks
        filteredTasks.forEach(task => {
            const taskElement = this.createTaskElement(task);
            container.appendChild(taskElement);
        });
    }

    createTaskElement(task) {
        const taskDiv = document.createElement('div');
        taskDiv.className = `task-item bg-white dark:bg-gray-700 rounded-lg p-4 mb-3 border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-all duration-200 ${
            task.completed ? 'completed-task' : ''
        } priority-${task.priority} ${
            this.isOverdue(task.dueDate) && !task.completed ? 'overdue' : ''
        } ${
            this.isDueToday(task.dueDate) && !task.completed ? 'due-today' : ''
        }`;

        taskDiv.draggable = true;
        taskDiv.dataset.taskId = task.id;

        // Add drag event listeners
        taskDiv.addEventListener('dragstart', (e) => this.handleDragStart(e, task.id));
        taskDiv.addEventListener('dragend', this.handleDragEnd.bind(this));
        taskDiv.addEventListener('dragover', this.handleDragOver.bind(this));
        taskDiv.addEventListener('dragleave', this.handleDragLeave.bind(this));
        taskDiv.addEventListener('drop', (e) => this.handleDrop(e, task.id));

        taskDiv.innerHTML = `
            <div class="flex items-start space-x-3">
                <!-- Checkbox -->
                <button class="task-checkbox mt-1 w-5 h-5 rounded border-2 border-gray-300 dark:border-gray-500 flex items-center justify-center transition-colors ${
                    task.completed ? 'bg-green-500 border-green-500' : 'hover:border-green-400'
                }" onclick="todoApp.toggleTask('${task.id}')">
                    ${task.completed ? '<i class="fas fa-check text-white text-xs"></i>' : ''}
                </button>

                <!-- Task Content -->
                <div class="flex-1 min-w-0">
                    <!-- Title and Actions -->
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h3 class="task-text text-lg font-medium text-gray-900 dark:text-white ${
                                task.completed ? 'line-through opacity-60' : ''
                            }" contenteditable="true" onblur="todoApp.handleTitleEdit(this, '${task.id}')">${task.title}</h3>

                            <!-- Task Meta -->
                            <div class="flex items-center space-x-4 mt-2 text-sm">
                                <!-- Category -->
                                <div class="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                                    <i class="${this.getCategoryIcon(task.category)}"></i>
                                    <span class="capitalize">${task.category}</span>
                                </div>

                                <!-- Priority -->
                                <div class="flex items-center space-x-1 ${this.getPriorityColor(task.priority)}">
                                    <i class="fas fa-flag"></i>
                                    <span class="capitalize">${task.priority}</span>
                                </div>

                                <!-- Due Date -->
                                ${task.dueDate ? `
                                    <div class="flex items-center space-x-1 ${
                                        this.isOverdue(task.dueDate) && !task.completed ? 'text-red-600 dark:text-red-400' :
                                        this.isDueToday(task.dueDate) && !task.completed ? 'text-orange-600 dark:text-orange-400' :
                                        'text-gray-600 dark:text-gray-400'
                                    }">
                                        <i class="fas fa-calendar"></i>
                                        <span>${this.formatDate(task.dueDate)}</span>
                                        ${this.isOverdue(task.dueDate) && !task.completed ? '<i class="fas fa-exclamation-triangle ml-1"></i>' : ''}
                                    </div>
                                ` : ''}
                            </div>

                            <!-- Description -->
                            ${task.description ? `
                                <p class="task-description mt-2 text-gray-600 dark:text-gray-400 text-sm" contenteditable="true" onblur="todoApp.handleDescriptionEdit(this, '${task.id}')">${task.description}</p>
                            ` : ''}
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center space-x-2 ml-4">
                            ${this.aiService && this.aiService.aiEnabled ? `
                                <button class="text-gray-400 hover:text-purple-500 transition-colors" onclick="todoApp.enhanceTaskWithAI('${task.id}')" title="AI Enhancement">
                                    <i class="fas fa-magic"></i>
                                </button>
                            ` : ''}
                            <button class="text-gray-400 hover:text-blue-500 transition-colors" onclick="todoApp.shareTask('${task.id}')" title="Share task">
                                <i class="fas fa-share"></i>
                            </button>
                            <button class="text-gray-400 hover:text-green-500 transition-colors" onclick="todoApp.addComment('${task.id}', prompt('Add comment:'))" title="Add comment">
                                <i class="fas fa-comment"></i>
                            </button>
                            <button class="text-gray-400 hover:text-red-500 transition-colors" onclick="todoApp.deleteTask('${task.id}')" title="Delete task">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors cursor-move" title="Drag to reorder">
                                <i class="fas fa-grip-vertical"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return taskDiv;
    }

    // Handle inline editing
    handleTitleEdit(element, taskId) {
        const newTitle = element.textContent.trim();
        if (newTitle && newTitle !== '') {
            this.editTask(taskId, 'title', newTitle);
        } else {
            // Restore original title if empty
            const task = this.tasks.find(t => t.id === taskId);
            if (task) {
                element.textContent = task.title;
            }
        }
    }

    handleDescriptionEdit(element, taskId) {
        const newDescription = element.textContent.trim();
        this.editTask(taskId, 'description', newDescription);
    }

    // Keyboard shortcuts
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + N: Focus on new task input
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            document.getElementById('task-title').focus();
        }

        // Ctrl/Cmd + F: Focus on search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            document.getElementById('search-tasks').focus();
        }

        // Ctrl/Cmd + D: Toggle dark mode
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            this.toggleTheme();
        }

        // Escape: Close modal or clear search
        if (e.key === 'Escape') {
            const modal = document.getElementById('delete-modal');
            if (!modal.classList.contains('hidden')) {
                this.hideDeleteModal();
            } else {
                const searchInput = document.getElementById('search-tasks');
                if (searchInput.value) {
                    searchInput.value = '';
                    this.handleSearch({ target: searchInput });
                }
            }
        }

        // Enter: Submit form if focused on task title
        if (e.key === 'Enter' && e.target.id === 'task-title') {
            e.preventDefault();
            document.getElementById('add-task-form').dispatchEvent(new Event('submit'));
        }
    }

    // Export/Import functionality
    exportTasks() {
        const dataStr = JSON.stringify(this.tasks, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `todo-tasks-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.showNotification('Tasks exported successfully!', 'success');
    }

    importTasks(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedTasks = JSON.parse(e.target.result);
                if (Array.isArray(importedTasks)) {
                    // Validate task structure
                    const validTasks = importedTasks.filter(task =>
                        task.id && task.title && typeof task.completed === 'boolean'
                    );

                    if (validTasks.length > 0) {
                        this.tasks = [...this.tasks, ...validTasks];
                        this.saveTasks();
                        this.renderTasks();
                        this.updateStats();
                        this.showNotification(`Imported ${validTasks.length} tasks successfully!`, 'success');
                    } else {
                        this.showNotification('No valid tasks found in the file', 'error');
                    }
                } else {
                    this.showNotification('Invalid file format', 'error');
                }
            } catch (error) {
                this.showNotification('Error reading file: ' + error.message, 'error');
            }
        };
        reader.readAsText(file);
    }

    // Clear all completed tasks
    clearCompleted() {
        const completedCount = this.tasks.filter(t => t.completed).length;
        if (completedCount === 0) {
            this.showNotification('No completed tasks to clear', 'info');
            return;
        }

        if (confirm(`Are you sure you want to delete ${completedCount} completed task(s)?`)) {
            this.tasks = this.tasks.filter(t => !t.completed);
            this.saveTasks();
            this.renderTasks();
            this.updateStats();
            this.showNotification(`Cleared ${completedCount} completed tasks`, 'success');
        }
    }

    // Get task statistics
    getTaskStats() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(t => t.completed).length;
        const overdue = this.tasks.filter(t => !t.completed && this.isOverdue(t.dueDate)).length;
        const dueToday = this.tasks.filter(t => !t.completed && this.isDueToday(t.dueDate)).length;

        return { total, completed, overdue, dueToday, pending: total - completed };
    }

    // AI-Powered Features
    async suggestCategoryWithAI(title, description) {
        if (!this.aiService || !this.aiService.aiEnabled) {
            return this.suggestCategory(title, description);
        }

        const settings = this.aiService.getSettings();
        if (settings.categorization === false) {
            return this.suggestCategory(title, description);
        }

        try {
            this.setAILoading('categorization', true);

            const prompt = `Analyze this task and suggest the most appropriate category:

Task: "${title}"
Description: "${description || 'No description provided'}"

Available categories: personal, work, shopping, health, learning, fitness, finance, creative, other

Respond with only the category name (lowercase, single word). Consider:
- Work-related tasks should be "work"
- Shopping/purchasing should be "shopping"
- Health/medical/fitness should be "health" or "fitness"
- Learning/education should be "learning"
- Creative projects should be "creative"
- Financial tasks should be "finance"
- Everything else should be "personal"

Category:`;

            const response = await this.aiService.makeRequest(prompt, {
                maxTokens: 20,
                temperature: 0.3,
                systemPrompt: 'You are a task categorization expert. Respond with only the category name.'
            });

            const suggestedCategory = response.content.trim().toLowerCase();
            const validCategories = ['personal', 'work', 'shopping', 'health', 'learning', 'fitness', 'finance', 'creative', 'other'];

            if (validCategories.includes(suggestedCategory)) {
                this.showNotification(`AI suggested category: ${suggestedCategory}`, 'info');
                return suggestedCategory;
            }

            return this.suggestCategory(title, description);

        } catch (error) {
            console.error('AI categorization failed:', error);
            return this.suggestCategory(title, description);
        } finally {
            this.setAILoading('categorization', false);
        }
    }

    // Fallback pattern-based categorization
    suggestCategory(title, description) {
        const text = (title + ' ' + description).toLowerCase();

        for (const [category, patterns] of Object.entries(this.aiPatterns)) {
            if (patterns.some(pattern => text.includes(pattern))) {
                return category;
            }
        }

        return 'personal'; // Default fallback
    }

    // Generate initial AI suggestions on app startup
    generateInitialAISuggestions() {
        // Use real AI if available and enabled, otherwise fallback to mock
        if (this.aiService && this.aiService.aiEnabled) {
            this.generateAISuggestionsReal();
        } else {
            this.generateAISuggestions();
        }
    }

    // AI-powered task suggestions
    async generateAISuggestionsReal() {
        if (!this.aiService || !this.aiService.aiEnabled) {
            return this.generateAISuggestions(); // Fallback to mock
        }

        const settings = this.aiService.getSettings();
        if (settings.suggestions === false) {
            return this.generateAISuggestions(); // Fallback to mock
        }

        try {
            this.setAILoading('suggestions', true);

            const now = new Date();
            const timeContext = this.getTimeContext(now);
            const taskContext = this.getTaskContext();

            const prompt = `Based on the current context, suggest 3 helpful tasks for a productivity-focused user:

Current time: ${timeContext}
User's recent tasks: ${taskContext}

Please suggest 3 practical, actionable tasks that would be valuable right now. For each task, provide:
1. Title (concise, actionable)
2. Description (brief explanation)
3. Category (personal/work/health/learning/other)
4. Priority (low/medium/high)
5. Reason (why this task is relevant now)

Format as JSON array:
[
  {
    "title": "Task title",
    "description": "Task description",
    "category": "category",
    "priority": "priority",
    "reason": "Why this is relevant"
  }
]`;

            const response = await this.aiService.makeRequest(prompt, {
                maxTokens: 800,
                temperature: 0.7,
                systemPrompt: 'You are a productivity expert. Suggest practical, actionable tasks based on context.'
            });

            try {
                const suggestions = JSON.parse(response.content);
                if (Array.isArray(suggestions) && suggestions.length > 0) {
                    this.aiSuggestions = suggestions.slice(0, 3);
                    this.renderAISuggestions();
                    return;
                }
            } catch (parseError) {
                console.error('Failed to parse AI suggestions:', parseError);
            }

            // Fallback to mock suggestions
            this.generateAISuggestions();

        } catch (error) {
            console.error('AI suggestions failed:', error);
            this.generateAISuggestions(); // Fallback to mock
        } finally {
            this.setAILoading('suggestions', false);
        }
    }

    getTimeContext(now) {
        const hour = now.getHours();
        const day = now.toLocaleDateString('en-US', { weekday: 'long' });
        const timeOfDay = hour < 12 ? 'morning' : hour < 17 ? 'afternoon' : 'evening';
        return `${day} ${timeOfDay} (${hour}:${now.getMinutes().toString().padStart(2, '0')})`;
    }

    getTaskContext() {
        const recentTasks = this.tasks
            .filter(t => {
                const taskDate = new Date(t.createdAt);
                const daysDiff = (Date.now() - taskDate) / (1000 * 60 * 60 * 24);
                return daysDiff <= 7;
            })
            .slice(0, 5)
            .map(t => `${t.title} (${t.category}, ${t.completed ? 'completed' : 'pending'})`)
            .join('; ');

        return recentTasks || 'No recent tasks';
    }

    // Natural language task parsing
    async parseNaturalLanguageTask(transcript) {
        if (!this.aiService || !this.aiService.aiEnabled) {
            document.getElementById('task-title').value = transcript;
            this.showNotification('Voice input captured!', 'success');
            return;
        }

        try {
            this.setAILoading('nlp', true);

            const prompt = `Parse this natural language input into a structured task:

Input: "${transcript}"

Extract and format as JSON:
{
  "title": "Clear, actionable task title",
  "description": "Additional details if any",
  "category": "personal/work/shopping/health/learning/other",
  "priority": "low/medium/high",
  "dueDate": "YYYY-MM-DD or null",
  "estimatedTime": "minutes as number or null"
}

Examples:
- "Call John tomorrow at 3pm" → title: "Call John", dueDate: tomorrow's date
- "Buy groceries this weekend" → title: "Buy groceries", category: "shopping"
- "Finish the report by Friday" → title: "Finish the report", category: "work", dueDate: Friday's date

Respond with only the JSON object.`;

            const response = await this.aiService.makeRequest(prompt, {
                maxTokens: 300,
                temperature: 0.3,
                systemPrompt: 'You are a task parsing expert. Extract structured data from natural language.'
            });

            try {
                const taskData = JSON.parse(response.content);

                // Populate form fields
                if (taskData.title) document.getElementById('task-title').value = taskData.title;
                if (taskData.description) document.getElementById('task-description').value = taskData.description;
                if (taskData.category) document.getElementById('task-category').value = taskData.category;
                if (taskData.priority) document.getElementById('task-priority').value = taskData.priority;
                if (taskData.dueDate) document.getElementById('task-due-date').value = taskData.dueDate;
                if (taskData.estimatedTime) document.getElementById('task-estimated-time').value = taskData.estimatedTime.toString();

                this.showNotification('AI parsed your voice input!', 'success');

            } catch (parseError) {
                // Fallback to simple input
                document.getElementById('task-title').value = transcript;
                this.showNotification('Voice input captured!', 'success');
            }

        } catch (error) {
            console.error('NLP parsing failed:', error);
            document.getElementById('task-title').value = transcript;
            this.showNotification('Voice input captured!', 'success');
        } finally {
            this.setAILoading('nlp', false);
        }
    }

    generateAISuggestions() {
        const suggestions = [];
        const now = new Date();
        const hour = now.getHours();
        const dayOfWeek = now.getDay();

        // Time-based suggestions
        if (hour < 10) {
            suggestions.push({
                title: 'Plan your day',
                description: 'Review your tasks and set priorities for today',
                category: 'personal',
                priority: 'medium',
                reason: 'Morning planning helps improve productivity'
            });
        }

        if (hour > 17) {
            suggestions.push({
                title: 'Review today\'s accomplishments',
                description: 'Reflect on what you completed today',
                category: 'personal',
                priority: 'low',
                reason: 'Evening reflection improves learning'
            });
        }

        // Day-based suggestions
        if (dayOfWeek === 1) { // Monday
            suggestions.push({
                title: 'Weekly planning session',
                description: 'Set goals and priorities for the week',
                category: 'work',
                priority: 'high',
                reason: 'Monday planning sets the tone for the week'
            });
        }

        if (dayOfWeek === 5) { // Friday
            suggestions.push({
                title: 'Weekly review and cleanup',
                description: 'Review completed tasks and prepare for next week',
                category: 'work',
                priority: 'medium',
                reason: 'Friday reviews help close the week effectively'
            });
        }

        // Pattern-based suggestions
        const recentTasks = this.tasks.filter(t => {
            const taskDate = new Date(t.createdAt);
            const daysDiff = (now - taskDate) / (1000 * 60 * 60 * 24);
            return daysDiff <= 7;
        });

        const categories = recentTasks.reduce((acc, task) => {
            acc[task.category] = (acc[task.category] || 0) + 1;
            return acc;
        }, {});

        const topCategory = Object.keys(categories).reduce((a, b) =>
            categories[a] > categories[b] ? a : b, 'personal');

        if (this.commonTasks.length > 0) {
            const randomTask = this.commonTasks[Math.floor(Math.random() * this.commonTasks.length)];
            suggestions.push({
                title: randomTask,
                description: 'AI suggested based on common productivity patterns',
                category: topCategory,
                priority: 'medium',
                reason: 'Based on your recent activity patterns'
            });
        }

        this.aiSuggestions = suggestions.slice(0, 3);
        this.renderAISuggestions();
    }

    renderAISuggestions() {
        const container = document.getElementById('suggestions-container');
        if (!container) return;

        if (this.aiSuggestions.length === 0) {
            container.innerHTML = '<p class="text-gray-500 dark:text-gray-400 text-sm">No suggestions available at the moment.</p>';
            return;
        }

        container.innerHTML = this.aiSuggestions.map(suggestion => `
            <div class="bg-white dark:bg-gray-700 rounded-lg p-4 border border-purple-200 dark:border-purple-600 hover:shadow-md transition-shadow">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 dark:text-white">${suggestion.title}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">${suggestion.description}</p>
                        <div class="flex items-center space-x-2 mt-2">
                            <span class="text-xs px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded">${suggestion.category}</span>
                            <span class="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded">${suggestion.priority}</span>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 italic">${suggestion.reason}</p>
                    </div>
                    <button onclick="todoApp.addAISuggestion('${suggestion.title}', '${suggestion.description}', '${suggestion.category}', '${suggestion.priority}')"
                            class="ml-3 px-3 py-1 text-sm bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                        Add
                    </button>
                </div>
            </div>
        `).join('');
    }

    addAISuggestion(title, description, category, priority) {
        document.getElementById('task-title').value = title;
        document.getElementById('task-description').value = description;
        document.getElementById('task-category').value = category;
        document.getElementById('task-priority').value = priority;

        this.showNotification('AI suggestion applied to form!', 'success');

        // Scroll to form
        document.getElementById('task-title').scrollIntoView({ behavior: 'smooth' });
        document.getElementById('task-title').focus();
    }

    handleSmartSuggestions(e) {
        const input = e.target.value.toLowerCase();
        const suggestionsContainer = document.getElementById('smart-suggestions');

        if (input.length < 2) {
            suggestionsContainer.classList.add('hidden');
            return;
        }

        const matches = this.commonTasks.filter(task =>
            task.toLowerCase().includes(input)
        ).slice(0, 5);

        if (matches.length > 0) {
            suggestionsContainer.innerHTML = matches.map(task => `
                <div class="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-sm"
                     onclick="todoApp.selectSuggestion('${task}')">
                    ${task}
                </div>
            `).join('');
            suggestionsContainer.classList.remove('hidden');
        } else {
            suggestionsContainer.classList.add('hidden');
        }
    }

    selectSuggestion(task) {
        document.getElementById('task-title').value = task;
        document.getElementById('smart-suggestions').classList.add('hidden');
    }

    async showAIAssist() {
        const title = document.getElementById('task-title').value.trim();
        if (!title) {
            this.showNotification('Enter a task title first to get AI assistance', 'info');
            return;
        }

        // AI-powered suggestions for improving the task
        const suggestions = [
            'Consider breaking this into smaller subtasks',
            'Add a specific deadline to increase accountability',
            'Set an estimated time to better plan your day',
            'Add relevant tags for better organization'
        ];

        const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
        this.showNotification(`AI Tip: ${randomSuggestion}`, 'info');

        // Auto-suggest category and priority with AI
        if (this.aiService && this.aiService.aiEnabled) {
            try {
                const suggestedCategory = await this.suggestCategoryWithAI(title, '');
                if (suggestedCategory && suggestedCategory !== 'personal') {
                    document.getElementById('task-category').value = suggestedCategory;
                }

                const suggestedPriority = await this.suggestTaskPriority(title, '', '', suggestedCategory || 'personal');
                if (suggestedPriority && suggestedPriority !== 'medium') {
                    document.getElementById('task-priority').value = suggestedPriority;
                    this.showNotification(`AI suggested: ${suggestedCategory || 'category'} / ${suggestedPriority} priority`, 'success');
                }
            } catch (error) {
                // Fallback to pattern matching
                const suggestedCategory = this.suggestCategory(title, '');
                if (suggestedCategory !== 'personal') {
                    document.getElementById('task-category').value = suggestedCategory;
                    this.showNotification(`Suggested category: ${suggestedCategory}`, 'success');
                }
            }
        } else {
            // Fallback to pattern matching
            const suggestedCategory = this.suggestCategory(title, '');
            if (suggestedCategory !== 'personal') {
                document.getElementById('task-category').value = suggestedCategory;
                this.showNotification(`Suggested category: ${suggestedCategory}`, 'success');
            }
        }
    }

    toggleVoiceInput() {
        if (!this.speechRecognition) {
            this.showNotification('Voice input not supported in this browser', 'error');
            return;
        }

        const button = document.getElementById('voice-input');
        if (button.classList.contains('recording')) {
            this.speechRecognition.stop();
            button.classList.remove('recording');
            button.innerHTML = '<i class="fas fa-microphone mr-1"></i>Voice';
        } else {
            this.speechRecognition.start();
            button.classList.add('recording');
            button.innerHTML = '<i class="fas fa-microphone-slash mr-1"></i>Recording...';
            this.showNotification('Listening... Speak now!', 'info');
        }
    }

    // Analytics Methods
    loadAnalytics() {
        const saved = localStorage.getItem('todoAnalytics');
        return saved ? JSON.parse(saved) : {
            dailyCompletions: {},
            categoryStats: {},
            productivityScore: 85,
            streak: 0,
            totalTimeSpent: 0,
            averageCompletionTime: 0
        };
    }

    saveAnalytics() {
        localStorage.setItem('todoAnalytics', JSON.stringify(this.analytics));
    }

    updateAnalytics(action, task) {
        const today = new Date().toISOString().split('T')[0];

        switch (action) {
            case 'taskCompleted':
                this.analytics.dailyCompletions[today] = (this.analytics.dailyCompletions[today] || 0) + 1;
                this.analytics.categoryStats[task.category] = (this.analytics.categoryStats[task.category] || 0) + 1;
                this.updateStreak();
                break;
            case 'taskAdded':
                break;
        }

        this.saveAnalytics();
    }

    updateStreak() {
        const today = new Date();
        let streak = 0;

        for (let i = 0; i < 365; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];

            if (this.analytics.dailyCompletions[dateStr] > 0) {
                streak++;
            } else if (i > 0) {
                break;
            }
        }

        this.analytics.streak = streak;
    }

    toggleAnalytics() {
        this.isAnalyticsVisible = !this.isAnalyticsVisible;
        const dashboard = document.getElementById('analytics-dashboard');
        const mainContent = document.getElementById('main-content');

        if (this.isAnalyticsVisible) {
            dashboard.classList.remove('hidden');
            mainContent.classList.add('hidden');
            this.updateAnalyticsDashboard();
        } else {
            dashboard.classList.add('hidden');
            mainContent.classList.remove('hidden');
        }
    }

    updateAnalyticsDashboard() {
        const score = this.calculateProductivityScore();
        document.getElementById('productivity-score').textContent = score;
        document.getElementById('productivity-bar').style.width = score + '%';

        document.getElementById('streak-count').textContent = this.analytics.streak;
        this.updateTimeTracking();

        if (this.charts.productivity) {
            this.charts.productivity.data.labels = this.getLast7Days();
            this.charts.productivity.data.datasets[0].data = this.getProductivityData();
            this.charts.productivity.update();
        }

        if (this.charts.distribution) {
            this.charts.distribution.data.datasets[0].data = this.getCategoryDistribution();
            this.charts.distribution.update();
        }

        this.updateCategoryPerformance();
    }

    calculateProductivityScore() {
        const completedTasks = this.tasks.filter(t => t.completed).length;
        const totalTasks = this.tasks.length;
        const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

        const streakBonus = Math.min(this.analytics.streak * 2, 20);
        const score = Math.min(Math.round(completionRate + streakBonus), 100);

        return score;
    }

    getLast7Days() {
        const days = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            days.push(date.toLocaleDateString('en-US', { weekday: 'short' }));
        }
        return days;
    }

    getLast30Days() {
        const days = [];
        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            days.push(date.getDate().toString());
        }
        return days;
    }

    getProductivityData() {
        const data = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            data.push(this.analytics.dailyCompletions[dateStr] || 0);
        }
        return data;
    }

    getCategoryDistribution() {
        const categories = ['personal', 'work', 'shopping', 'health', 'learning', 'other'];
        return categories.map(cat =>
            this.tasks.filter(t => t.category === cat).length
        );
    }

    getTimelineData() {
        const data = [];
        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            data.push(this.analytics.dailyCompletions[dateStr] || 0);
        }
        return data;
    }

    updateTimeTracking() {
        const today = Math.floor(Math.random() * 8) + 1;
        const week = today * 7 + Math.floor(Math.random() * 10);
        const average = Math.floor(week / 7);

        document.getElementById('time-today').textContent = `${today}h 0m`;
        document.getElementById('time-week').textContent = `${week}h 0m`;
        document.getElementById('time-average').textContent = `${average}h 0m`;
    }

    updateCategoryPerformance() {
        const container = document.getElementById('category-performance');
        const categories = ['personal', 'work', 'shopping', 'health', 'learning'];

        container.innerHTML = categories.map(category => {
            const total = this.tasks.filter(t => t.category === category).length;
            const completed = this.tasks.filter(t => t.category === category && t.completed).length;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

            return `
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">${category}</span>
                    <div class="flex items-center space-x-2">
                        <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div class="bg-primary-500 h-2 rounded-full" style="width: ${percentage}%"></div>
                        </div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">${percentage}%</span>
                    </div>
                </div>
            `;
        }).join('');
    }

    // Pomodoro Timer Methods
    showPomodoroModal() {
        document.getElementById('pomodoro-modal').classList.remove('hidden');
        document.getElementById('pomodoro-modal').classList.add('flex');
        this.updatePomodoroDisplay();
    }

    hidePomodoroModal() {
        document.getElementById('pomodoro-modal').classList.add('hidden');
        document.getElementById('pomodoro-modal').classList.remove('flex');
    }

    startPomodoro() {
        if (this.pomodoro.timer) return;

        this.pomodoro.isRunning = true;
        this.pomodoro.timer = setInterval(() => {
            this.pomodoro.timeLeft--;
            this.updatePomodoroDisplay();

            if (this.pomodoro.timeLeft <= 0) {
                this.pomodoroComplete();
            }
        }, 1000);

        this.showNotification('Pomodoro timer started!', 'success');
    }

    pausePomodoro() {
        if (this.pomodoro.timer) {
            clearInterval(this.pomodoro.timer);
            this.pomodoro.timer = null;
            this.pomodoro.isRunning = false;
            this.showNotification('Pomodoro timer paused', 'info');
        }
    }

    resetPomodoro() {
        if (this.pomodoro.timer) {
            clearInterval(this.pomodoro.timer);
            this.pomodoro.timer = null;
        }

        this.pomodoro.isRunning = false;
        this.pomodoro.timeLeft = 1500; // 25 minutes
        this.updatePomodoroDisplay();
        this.showNotification('Pomodoro timer reset', 'info');
    }

    setPomodoroTime(minutes) {
        if (this.pomodoro.isRunning) return;

        this.pomodoro.timeLeft = minutes * 60;
        this.updatePomodoroDisplay();
    }

    updatePomodoroDisplay() {
        const minutes = Math.floor(this.pomodoro.timeLeft / 60);
        const seconds = this.pomodoro.timeLeft % 60;
        const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        const displayElement = document.getElementById('pomodoro-display');
        if (displayElement) {
            displayElement.textContent = display;
        }
    }

    pomodoroComplete() {
        clearInterval(this.pomodoro.timer);
        this.pomodoro.timer = null;
        this.pomodoro.isRunning = false;

        // Show completion notification
        this.showNotification('Pomodoro session complete! Take a break.', 'success');

        // Play notification sound (if supported)
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('Pomodoro Complete!', {
                body: 'Time for a break!',
                icon: '/favicon.ico'
            });
        }

        // Auto-switch to break mode
        this.pomodoro.mode = this.pomodoro.mode === 'work' ? 'break' : 'work';
        this.pomodoro.timeLeft = this.pomodoro.mode === 'work' ? 1500 : 300; // 25min work, 5min break
        this.updatePomodoroDisplay();
    }

    // Advanced Data Management
    encryptData(data) {
        // Simple base64 encoding for demo purposes
        return btoa(JSON.stringify(data));
    }

    decryptData(encryptedData) {
        try {
            return JSON.parse(atob(encryptedData));
        } catch (e) {
            return null;
        }
    }

    createBackup() {
        const backup = {
            tasks: this.tasks,
            analytics: this.analytics,
            timestamp: new Date().toISOString(),
            version: '2.0'
        };

        const encrypted = this.encryptData(backup);
        const blob = new Blob([encrypted], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `todo-backup-${new Date().toISOString().split('T')[0]}.bak`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.showNotification('Encrypted backup created successfully!', 'success');
    }

    restoreBackup(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const decrypted = this.decryptData(e.target.result);
                if (decrypted && decrypted.tasks && decrypted.analytics) {
                    this.tasks = decrypted.tasks;
                    this.analytics = decrypted.analytics;
                    this.saveTasks();
                    this.saveAnalytics();
                    this.renderTasks();
                    this.updateStats();
                    this.updateAnalyticsDashboard();
                    this.showNotification('Backup restored successfully!', 'success');
                } else {
                    this.showNotification('Invalid backup file format', 'error');
                }
            } catch (error) {
                this.showNotification('Error restoring backup: ' + error.message, 'error');
            }
        };
        reader.readAsText(file);
    }

    // Smart Notifications
    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    this.showNotification('Notifications enabled!', 'success');
                }
            });
        }
    }

    scheduleTaskReminder(task) {
        if (!task.dueDate || Notification.permission !== 'granted') return;

        const dueTime = new Date(task.dueDate).getTime();
        const now = Date.now();
        const timeUntilDue = dueTime - now;

        if (timeUntilDue > 0 && timeUntilDue <= 24 * 60 * 60 * 1000) { // Within 24 hours
            setTimeout(() => {
                if (!task.completed) {
                    new Notification('Task Due Soon!', {
                        body: `"${task.title}" is due soon`,
                        icon: '/favicon.ico'
                    });
                }
            }, Math.max(0, timeUntilDue - 60 * 60 * 1000)); // 1 hour before
        }
    }

    // Collaboration Features
    shareTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;

        const shareData = {
            title: `Shared Task: ${task.title}`,
            text: `${task.title}\n${task.description || ''}\nDue: ${task.dueDate || 'No due date'}\nPriority: ${task.priority}`,
            url: window.location.href
        };

        if (navigator.share) {
            navigator.share(shareData);
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareData.text).then(() => {
                this.showNotification('Task details copied to clipboard!', 'success');
            });
        }
    }

    addComment(taskId, comment) {
        const task = this.tasks.find(t => t.id === taskId);
        if (task && comment.trim()) {
            if (!task.comments) task.comments = [];
            task.comments.push({
                id: this.generateId(),
                text: comment.trim(),
                timestamp: new Date().toISOString(),
                author: 'You'
            });
            this.saveTasks();
            this.renderTasks();
            this.showNotification('Comment added!', 'success');
        }
    }

    // Automation Rules
    applyAutomationRules(task) {
        // Auto-assign due dates based on priority
        if (!task.dueDate) {
            const today = new Date();
            switch (task.priority) {
                case 'urgent':
                    today.setDate(today.getDate() + 1);
                    task.dueDate = today.toISOString().split('T')[0];
                    break;
                case 'high':
                    today.setDate(today.getDate() + 3);
                    task.dueDate = today.toISOString().split('T')[0];
                    break;
                case 'medium':
                    today.setDate(today.getDate() + 7);
                    task.dueDate = today.toISOString().split('T')[0];
                    break;
            }
        }

        // Auto-tag based on content
        if (!task.tags || task.tags.length === 0) {
            const autoTags = [];
            const text = (task.title + ' ' + task.description).toLowerCase();

            if (text.includes('meeting') || text.includes('call')) autoTags.push('meeting');
            if (text.includes('urgent') || text.includes('asap')) autoTags.push('urgent');
            if (text.includes('review') || text.includes('check')) autoTags.push('review');

            task.tags = autoTags;
        }

        return task;
    }

    // View Management
    switchView(view) {
        // Hide all views
        document.getElementById('main-content').classList.add('hidden');
        document.getElementById('analytics-dashboard').classList.add('hidden');
        document.getElementById('habit-tracker').classList.add('hidden');
        document.getElementById('goal-setting').classList.add('hidden');

        // Update navigation buttons
        document.querySelectorAll('#tasks-view, #analytics-view, #habits-view, #goals-view').forEach(btn => {
            btn.classList.remove('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-white', 'shadow-sm');
            btn.classList.add('text-gray-600', 'dark:text-gray-400');
        });

        // Show selected view and update button
        switch (view) {
            case 'tasks':
                document.getElementById('main-content').classList.remove('hidden');
                document.getElementById('tasks-view').classList.add('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-white', 'shadow-sm');
                document.getElementById('tasks-view').classList.remove('text-gray-600', 'dark:text-gray-400');
                break;
            case 'analytics':
                document.getElementById('analytics-dashboard').classList.remove('hidden');
                document.getElementById('analytics-view').classList.add('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-white', 'shadow-sm');
                document.getElementById('analytics-view').classList.remove('text-gray-600', 'dark:text-gray-400');
                this.updateAnalyticsDashboard();
                break;
            case 'habits':
                document.getElementById('habit-tracker').classList.remove('hidden');
                document.getElementById('habits-view').classList.add('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-white', 'shadow-sm');
                document.getElementById('habits-view').classList.remove('text-gray-600', 'dark:text-gray-400');
                this.renderHabits();
                break;
            case 'goals':
                document.getElementById('goal-setting').classList.remove('hidden');
                document.getElementById('goals-view').classList.add('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-white', 'shadow-sm');
                document.getElementById('goals-view').classList.remove('text-gray-600', 'dark:text-gray-400');
                this.renderGoals();
                break;
        }

        this.currentView = view;
    }

    // Advanced Search
    toggleAdvancedSearch() {
        const options = document.getElementById('advanced-search-options');
        options.classList.toggle('hidden');
    }

    handleAdvancedSearch() {
        const priority = document.getElementById('search-priority').value;
        const dueDate = document.getElementById('search-due-date').value;

        this.currentFilter.priority = priority;
        this.currentFilter.dueDate = dueDate;
        this.renderTasks();
    }

    // Habit Tracking
    loadHabits() {
        const saved = localStorage.getItem('todoHabits');
        return saved ? JSON.parse(saved) : [];
    }

    saveHabits() {
        localStorage.setItem('todoHabits', JSON.stringify(this.habits));
    }

    addHabit() {
        const name = prompt('Enter habit name:');
        if (!name) return;

        const habit = {
            id: this.generateId(),
            name: name.trim(),
            streak: 0,
            completions: {},
            createdAt: new Date().toISOString()
        };

        this.habits.push(habit);
        this.saveHabits();
        this.renderHabits();
        this.showNotification('Habit added successfully!', 'success');
    }

    toggleHabit(habitId) {
        const habit = this.habits.find(h => h.id === habitId);
        if (!habit) return;

        const today = new Date().toISOString().split('T')[0];
        habit.completions[today] = !habit.completions[today];

        // Update streak
        this.updateHabitStreak(habit);

        this.saveHabits();
        this.renderHabits();

        const message = habit.completions[today] ? 'Habit completed for today!' : 'Habit unmarked for today';
        this.showNotification(message, 'success');
    }

    updateHabitStreak(habit) {
        let streak = 0;
        const today = new Date();

        for (let i = 0; i < 365; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];

            if (habit.completions[dateStr]) {
                streak++;
            } else if (i > 0) {
                break;
            }
        }

        habit.streak = streak;
    }

    renderHabits() {
        const container = document.getElementById('habits-grid');
        if (!container) return;

        if (this.habits.length === 0) {
            container.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <i class="fas fa-calendar-check text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                    <h3 class="text-xl font-medium text-gray-500 dark:text-gray-400 mb-2">No habits yet</h3>
                    <p class="text-gray-400 dark:text-gray-500">Add your first habit to start tracking!</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.habits.map(habit => {
            const today = new Date().toISOString().split('T')[0];
            const isCompletedToday = habit.completions[today];

            return `
                <div class="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-medium text-gray-900 dark:text-white">${habit.name}</h3>
                        <button onclick="todoApp.deleteHabit('${habit.id}')" class="text-gray-400 hover:text-red-500">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </div>

                    <div class="text-center mb-3">
                        <div class="text-2xl font-bold text-orange-500">${habit.streak}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Day Streak</div>
                    </div>

                    <button onclick="todoApp.toggleHabit('${habit.id}')"
                            class="w-full py-2 rounded-lg transition-colors ${
                                isCompletedToday
                                    ? 'bg-green-500 text-white'
                                    : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                            }">
                        ${isCompletedToday ? '✓ Completed Today' : 'Mark Complete'}
                    </button>
                </div>
            `;
        }).join('');
    }

    deleteHabit(habitId) {
        if (confirm('Are you sure you want to delete this habit?')) {
            this.habits = this.habits.filter(h => h.id !== habitId);
            this.saveHabits();
            this.renderHabits();
            this.showNotification('Habit deleted', 'success');
        }
    }

    // Goal Setting
    loadGoals() {
        const saved = localStorage.getItem('todoGoals');
        return saved ? JSON.parse(saved) : { weekly: [], monthly: [] };
    }

    saveGoals() {
        localStorage.setItem('todoGoals', JSON.stringify(this.goals));
    }

    addGoal(type) {
        const title = prompt(`Enter ${type} goal:`);
        if (!title) return;

        const goal = {
            id: this.generateId(),
            title: title.trim(),
            completed: false,
            createdAt: new Date().toISOString()
        };

        this.goals[type].push(goal);
        this.saveGoals();
        this.renderGoals();
        this.showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} goal added!`, 'success');
    }

    toggleGoal(type, goalId) {
        const goal = this.goals[type].find(g => g.id === goalId);
        if (goal) {
            goal.completed = !goal.completed;
            this.saveGoals();
            this.renderGoals();

            const message = goal.completed ? 'Goal completed!' : 'Goal marked as incomplete';
            this.showNotification(message, 'success');
        }
    }

    deleteGoal(type, goalId) {
        if (confirm('Are you sure you want to delete this goal?')) {
            this.goals[type] = this.goals[type].filter(g => g.id !== goalId);
            this.saveGoals();
            this.renderGoals();
            this.showNotification('Goal deleted', 'success');
        }
    }

    renderGoals() {
        ['weekly', 'monthly'].forEach(type => {
            const container = document.getElementById(`${type}-goals`);
            if (!container) return;

            const goals = this.goals[type];

            if (goals.length === 0) {
                container.innerHTML = `<p class="text-gray-500 dark:text-gray-400 text-sm">No ${type} goals yet</p>`;
                return;
            }

            container.innerHTML = goals.map(goal => `
                <div class="flex items-center justify-between p-2 bg-white dark:bg-gray-600 rounded border">
                    <div class="flex items-center space-x-2">
                        <button onclick="todoApp.toggleGoal('${type}', '${goal.id}')"
                                class="w-4 h-4 rounded border-2 border-gray-300 dark:border-gray-500 flex items-center justify-center transition-colors ${
                                    goal.completed ? 'bg-green-500 border-green-500' : 'hover:border-green-400'
                                }">
                            ${goal.completed ? '<i class="fas fa-check text-white text-xs"></i>' : ''}
                        </button>
                        <span class="text-sm ${goal.completed ? 'line-through opacity-60' : ''}">${goal.title}</span>
                    </div>
                    <button onclick="todoApp.deleteGoal('${type}', '${goal.id}')" class="text-gray-400 hover:text-red-500">
                        <i class="fas fa-trash text-xs"></i>
                    </button>
                </div>
            `).join('');
        });
    }

    // Calendar Integration
    exportToCalendar() {
        const tasksWithDates = this.tasks.filter(t => t.dueDate && !t.completed);

        if (tasksWithDates.length === 0) {
            this.showNotification('No tasks with due dates to export', 'info');
            return;
        }

        let icsContent = 'BEGIN:VCALENDAR\nVERSION:2.0\nPRODID:-//Todo App//EN\n';

        tasksWithDates.forEach(task => {
            const dueDate = new Date(task.dueDate);
            const dateStr = dueDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

            icsContent += `BEGIN:VEVENT\n`;
            icsContent += `UID:${task.id}@todoapp.com\n`;
            icsContent += `DTSTAMP:${new Date().toISOString().replace(/[-:]/g, '').split('.')[0]}Z\n`;
            icsContent += `DTSTART:${dateStr}\n`;
            icsContent += `SUMMARY:${task.title}\n`;
            icsContent += `DESCRIPTION:${task.description || ''}\n`;
            icsContent += `PRIORITY:${task.priority === 'high' ? '1' : task.priority === 'medium' ? '5' : '9'}\n`;
            icsContent += `END:VEVENT\n`;
        });

        icsContent += 'END:VCALENDAR';

        const blob = new Blob([icsContent], { type: 'text/calendar' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'todo-tasks.ics';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.showNotification('Calendar file exported successfully!', 'success');
    }

    // Webhook Support
    setupWebhook(url) {
        this.webhookUrl = url;
        localStorage.setItem('todoWebhook', url);
        this.showNotification('Webhook configured successfully!', 'success');
    }

    sendWebhook(event, data) {
        const webhookUrl = localStorage.getItem('todoWebhook');
        if (!webhookUrl) return;

        const payload = {
            event,
            data,
            timestamp: new Date().toISOString(),
            user: 'current_user'
        };

        fetch(webhookUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        }).catch(() => {
            // Silently fail for demo purposes
        });
    }

    // Advanced Automation
    setupAutomationRules() {
        // Check for overdue tasks every hour
        setInterval(() => {
            this.checkOverdueTasks();
        }, 60 * 60 * 1000);

        // Daily productivity summary
        setInterval(() => {
            this.generateDailySummary();
        }, 24 * 60 * 60 * 1000);

        // Auto-backup weekly
        setInterval(() => {
            this.createBackup();
        }, 7 * 24 * 60 * 60 * 1000);
    }

    checkOverdueTasks() {
        const overdueTasks = this.tasks.filter(t =>
            !t.completed && t.dueDate && this.isOverdue(t.dueDate)
        );

        if (overdueTasks.length > 0 && Notification.permission === 'granted') {
            new Notification('Overdue Tasks!', {
                body: `You have ${overdueTasks.length} overdue task(s)`,
                icon: '/favicon.ico'
            });
        }
    }

    generateDailySummary() {
        const today = new Date().toISOString().split('T')[0];
        const completedToday = this.tasks.filter(t =>
            t.completed && t.completedAt && t.completedAt.startsWith(today)
        ).length;

        if (completedToday > 0) {
            this.showNotification(`Daily Summary: You completed ${completedToday} task(s) today! 🎉`, 'success');
        }
    }

    // Performance Optimization
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Data Sync (Mock implementation)
    syncToCloud() {
        // Simulate cloud sync
        return new Promise((resolve) => {
            setTimeout(() => {
                this.showNotification('Data synced to cloud successfully!', 'success');
                resolve();
            }, 2000);
        });
    }

    // Advanced Theme System
    setCustomTheme(theme) {
        const themes = {
            ocean: {
                primary: '#0ea5e9',
                secondary: '#0284c7',
                accent: '#06b6d4'
            },
            forest: {
                primary: '#059669',
                secondary: '#047857',
                accent: '#10b981'
            },
            sunset: {
                primary: '#ea580c',
                secondary: '#dc2626',
                accent: '#f59e0b'
            }
        };

        if (themes[theme]) {
            const root = document.documentElement;
            Object.entries(themes[theme]).forEach(([key, value]) => {
                root.style.setProperty(`--color-${key}`, value);
            });

            localStorage.setItem('customTheme', theme);
            this.showNotification(`${theme.charAt(0).toUpperCase() + theme.slice(1)} theme applied!`, 'success');
        }
    }

    // Productivity Insights
    generateInsights() {
        const insights = [];
        const completedTasks = this.tasks.filter(t => t.completed);
        const totalTasks = this.tasks.length;

        // Completion rate insight
        const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;
        if (completionRate > 80) {
            insights.push('🎯 Excellent! You have a high task completion rate.');
        } else if (completionRate < 50) {
            insights.push('💡 Consider breaking down large tasks into smaller, manageable ones.');
        }

        // Category analysis
        const categoryStats = this.tasks.reduce((acc, task) => {
            acc[task.category] = (acc[task.category] || 0) + 1;
            return acc;
        }, {});

        const topCategory = Object.keys(categoryStats).reduce((a, b) =>
            categoryStats[a] > categoryStats[b] ? a : b, 'personal');

        insights.push(`📊 Your most active category is "${topCategory}".`);

        // Time-based insights
        const now = new Date();
        const recentTasks = this.tasks.filter(t => {
            const taskDate = new Date(t.createdAt);
            const daysDiff = (now - taskDate) / (1000 * 60 * 60 * 24);
            return daysDiff <= 7;
        });

        if (recentTasks.length > 10) {
            insights.push('⚡ You\'ve been very productive this week!');
        }

        return insights;
    }

    // AI Settings Management
    showAISettings() {
        document.getElementById('ai-settings-modal').classList.remove('hidden');
        document.getElementById('ai-settings-modal').classList.add('flex');
        this.loadAISettings();
    }

    hideAISettings() {
        document.getElementById('ai-settings-modal').classList.add('hidden');
        document.getElementById('ai-settings-modal').classList.remove('flex');
    }

    toggleAPIKeyVisibility() {
        const input = document.getElementById('api-key-input');
        const icon = document.getElementById('toggle-api-key').querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    async testAIConnection() {
        if (!this.aiService) {
            this.showNotification('AI service not available', 'error');
            return;
        }

        const button = document.getElementById('test-ai-connection');
        const originalText = button.textContent;

        try {
            button.textContent = 'Testing...';
            button.disabled = true;

            const success = await this.aiService.testConnection();

            if (success) {
                this.showNotification('AI connection successful!', 'success');
            } else {
                this.showNotification('AI connection failed', 'error');
            }

        } catch (error) {
            this.showNotification('Connection test failed: ' + error.message, 'error');
        } finally {
            button.textContent = originalText;
            button.disabled = false;
        }
    }

    saveAISettings() {
        if (!this.aiService) {
            this.showNotification('AI service not available', 'error');
            return;
        }

        try {
            // Save API key
            const apiKey = document.getElementById('api-key-input').value.trim();
            if (apiKey && apiKey !== 'sk-or-v1-demo-key') {
                this.aiService.setAPIKey(apiKey);
            }

            // Save model selection
            const selectedModel = document.getElementById('ai-model-select').value;
            if (selectedModel) {
                this.aiService.setModel(selectedModel);
            }

            // Save feature toggles
            const settings = {
                enabled: document.getElementById('ai-enabled-toggle').classList.contains('bg-purple-600'),
                categorization: document.getElementById('ai-categorization').checked,
                suggestions: document.getElementById('ai-suggestions').checked,
                prioritization: document.getElementById('ai-prioritization').checked,
                analytics: document.getElementById('ai-analytics').checked,
                anonymization: document.getElementById('data-anonymization').checked
            };

            this.aiService.updateSettings(settings);
            this.showNotification('AI settings saved successfully!', 'success');
            this.hideAISettings();

        } catch (error) {
            this.showNotification('Failed to save settings: ' + error.message, 'error');
        }
    }

    // AI Loading State Management
    setAILoading(feature, isLoading) {
        if (isLoading) {
            this.aiLoadingStates.add(feature);
        } else {
            this.aiLoadingStates.delete(feature);
        }

        this.updateAILoadingIndicators(feature, isLoading);
    }

    updateAILoadingIndicators(feature, isLoading) {
        // Update specific loading indicators based on feature
        switch (feature) {
            case 'suggestions':
                const suggestionsContainer = document.getElementById('suggestions-container');
                if (suggestionsContainer) {
                    if (isLoading) {
                        suggestionsContainer.innerHTML = `
                            <div class="flex items-center justify-center py-8">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                                <span class="ml-3 text-gray-600 dark:text-gray-400">AI is thinking...</span>
                            </div>
                        `;
                    }
                }
                break;

            case 'categorization':
                const categorySelect = document.getElementById('task-category');
                if (categorySelect && isLoading) {
                    this.showNotification('AI is analyzing task category...', 'info');
                }
                break;

            case 'nlp':
                const voiceButton = document.getElementById('voice-input');
                if (voiceButton) {
                    if (isLoading) {
                        voiceButton.innerHTML = '<i class="fas fa-brain mr-1"></i>Processing...';
                        voiceButton.disabled = true;
                    } else {
                        voiceButton.innerHTML = '<i class="fas fa-microphone mr-1"></i>Voice';
                        voiceButton.disabled = false;
                    }
                }
                break;
        }
    }

    // Enhanced AI Assistant
    async showAIAssist() {
        const title = document.getElementById('task-title').value.trim();
        if (!title) {
            this.showNotification('Enter a task title first to get AI assistance', 'info');
            return;
        }

        if (!this.aiService || !this.aiService.aiEnabled) {
            // Fallback to original mock functionality
            const suggestions = [
                'Consider breaking this into smaller subtasks',
                'Add a specific deadline to increase accountability',
                'Set an estimated time to better plan your day',
                'Add relevant tags for better organization'
            ];

            const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
            this.showNotification(`AI Tip: ${randomSuggestion}`, 'info');
            return;
        }

        try {
            this.setAILoading('assist', true);

            const description = document.getElementById('task-description').value.trim();

            const prompt = `Analyze this task and provide helpful suggestions for improvement:

Task: "${title}"
Description: "${description || 'No description provided'}"

Provide 2-3 specific, actionable suggestions to improve this task. Consider:
- Breaking down complex tasks into subtasks
- Adding missing details (deadlines, priorities, dependencies)
- Improving clarity and specificity
- Suggesting relevant tags or categories
- Estimating time requirements

Format as a brief, helpful response with numbered suggestions.`;

            const response = await this.aiService.makeRequest(prompt, {
                maxTokens: 400,
                temperature: 0.7,
                systemPrompt: 'You are a productivity expert helping users improve their task management.'
            });

            // Show AI suggestions in a more prominent way
            this.showAIAssistanceModal(response.content);

        } catch (error) {
            console.error('AI assistance failed:', error);
            // Fallback to mock functionality
            const suggestions = [
                'Consider breaking this into smaller subtasks',
                'Add a specific deadline to increase accountability',
                'Set an estimated time to better plan your day'
            ];

            const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
            this.showNotification(`AI Tip: ${randomSuggestion}`, 'info');
        } finally {
            this.setAILoading('assist', false);
        }
    }

    showAIAssistanceModal(suggestions) {
        // Create a temporary modal for AI suggestions
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md mx-4 animate-bounce-in">
                <div class="flex items-center mb-4">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">AI Suggestions</h3>
                </div>
                <div class="text-gray-700 dark:text-gray-300 mb-6 whitespace-pre-line">${suggestions}</div>
                <button onclick="this.closest('.fixed').remove()"
                        class="w-full px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                    Got it!
                </button>
            </div>
        `;

        document.body.appendChild(modal);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (modal.parentNode) {
                modal.remove();
            }
        }, 10000);
    }

    // AI-Powered Task Enhancement
    async enhanceTaskWithAI(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task || !this.aiService || !this.aiService.aiEnabled) return;

        try {
            this.setAILoading('enhancement', true);

            const prompt = `Analyze and enhance this task with suggestions:

Task: "${task.title}"
Description: "${task.description || 'No description'}"
Category: ${task.category}
Priority: ${task.priority}
Due Date: ${task.dueDate || 'Not set'}

Provide enhancement suggestions in JSON format:
{
  "improvedTitle": "More specific and actionable title",
  "improvedDescription": "Enhanced description with more details",
  "suggestedPriority": "low/medium/high/urgent with reasoning",
  "subtasks": ["subtask 1", "subtask 2", "subtask 3"],
  "estimatedTime": "time in minutes",
  "tags": ["tag1", "tag2"],
  "tips": "Productivity tips for completing this task"
}

Focus on making the task more actionable and well-defined.`;

            const response = await this.aiService.makeRequest(prompt, {
                maxTokens: 600,
                temperature: 0.7,
                systemPrompt: 'You are a task optimization expert. Provide practical enhancements.'
            });

            try {
                const enhancements = JSON.parse(response.content);
                this.showTaskEnhancementModal(task, enhancements);
            } catch (parseError) {
                this.showNotification('AI provided suggestions but format was unclear', 'info');
            }

        } catch (error) {
            console.error('Task enhancement failed:', error);
            this.showNotification('AI enhancement temporarily unavailable', 'error');
        } finally {
            this.setAILoading('enhancement', false);
        }
    }

    showTaskEnhancementModal(task, enhancements) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-2xl mx-4 animate-bounce-in max-h-96 overflow-y-auto">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-magic text-purple-500 mr-2"></i>
                        AI Task Enhancement
                    </h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    ${enhancements.improvedTitle ? `
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white mb-1">Improved Title:</h4>
                            <p class="text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-2 rounded">${enhancements.improvedTitle}</p>
                        </div>
                    ` : ''}

                    ${enhancements.improvedDescription ? `
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white mb-1">Enhanced Description:</h4>
                            <p class="text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-2 rounded">${enhancements.improvedDescription}</p>
                        </div>
                    ` : ''}

                    ${enhancements.subtasks && enhancements.subtasks.length > 0 ? `
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white mb-1">Suggested Subtasks:</h4>
                            <ul class="list-disc list-inside text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                                ${enhancements.subtasks.map(subtask => `<li>${subtask}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${enhancements.tips ? `
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white mb-1">Productivity Tips:</h4>
                            <p class="text-gray-700 dark:text-gray-300 bg-blue-50 dark:bg-blue-900/20 p-2 rounded">${enhancements.tips}</p>
                        </div>
                    ` : ''}
                </div>

                <div class="flex space-x-3 mt-6">
                    <button onclick="todoApp.applyTaskEnhancements('${task.id}', ${JSON.stringify(enhancements).replace(/"/g, '&quot;')}); this.closest('.fixed').remove();"
                            class="flex-1 px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                        Apply Suggestions
                    </button>
                    <button onclick="this.closest('.fixed').remove()"
                            class="flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                        Dismiss
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    applyTaskEnhancements(taskId, enhancements) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;

        // Apply enhancements
        if (enhancements.improvedTitle) task.title = enhancements.improvedTitle;
        if (enhancements.improvedDescription) task.description = enhancements.improvedDescription;
        if (enhancements.suggestedPriority) task.priority = enhancements.suggestedPriority.split(' ')[0]; // Extract priority word
        if (enhancements.estimatedTime) task.estimatedTime = parseInt(enhancements.estimatedTime);
        if (enhancements.tags) task.tags = [...(task.tags || []), ...enhancements.tags];

        task.updatedAt = new Date().toISOString();
        task.aiEnhanced = true;

        this.saveTasks();
        this.renderTasks();
        this.showNotification('Task enhanced with AI suggestions!', 'success');
    }

    // AI-Powered Smart Prioritization
    async suggestTaskPriority(title, description, dueDate, category) {
        if (!this.aiService || !this.aiService.aiEnabled) {
            return 'medium'; // Default fallback
        }

        const settings = this.aiService.getSettings();
        if (settings.prioritization === false) {
            return 'medium';
        }

        try {
            const prompt = `Analyze this task and suggest the optimal priority level:

Task: "${title}"
Description: "${description || 'No description'}"
Category: ${category}
Due Date: ${dueDate || 'Not specified'}
Current Date: ${new Date().toISOString().split('T')[0]}

Consider:
- Urgency (how soon it needs to be done)
- Importance (impact if not completed)
- Dependencies (blocks other tasks)
- Effort required
- Category context

Respond with only one word: low, medium, high, or urgent

Priority:`;

            const response = await this.aiService.makeRequest(prompt, {
                maxTokens: 10,
                temperature: 0.3,
                systemPrompt: 'You are a task prioritization expert. Consider urgency and importance.'
            });

            const priority = response.content.trim().toLowerCase();
            const validPriorities = ['low', 'medium', 'high', 'urgent'];

            if (validPriorities.includes(priority)) {
                return priority;
            }

            return 'medium';

        } catch (error) {
            console.error('AI prioritization failed:', error);
            return 'medium';
        }
    }

    // AI-Powered Analytics Insights
    async generateAIAnalyticsInsights() {
        if (!this.aiService || !this.aiService.aiEnabled) {
            return this.generateInsights(); // Fallback to mock
        }

        const settings = this.aiService.getSettings();
        if (settings.analytics === false) {
            return this.generateInsights();
        }

        try {
            this.setAILoading('analytics', true);

            const taskSummary = this.getTaskSummaryForAI();

            const prompt = `Analyze this user's task management data and provide personalized productivity insights:

${taskSummary}

Provide 3-5 specific, actionable insights about:
- Productivity patterns and trends
- Areas for improvement
- Strengths to leverage
- Recommendations for better task management
- Time management suggestions

Format as an array of insight objects:
[
  {
    "type": "pattern/improvement/strength/recommendation",
    "title": "Brief insight title",
    "description": "Detailed explanation",
    "action": "Specific action to take"
  }
]`;

            const response = await this.aiService.makeRequest(prompt, {
                maxTokens: 800,
                temperature: 0.7,
                systemPrompt: 'You are a productivity analytics expert. Provide actionable insights.'
            });

            try {
                const insights = JSON.parse(response.content);
                if (Array.isArray(insights)) {
                    return insights.map(insight =>
                        `${insight.title}: ${insight.description} ${insight.action ? '→ ' + insight.action : ''}`
                    );
                }
            } catch (parseError) {
                console.error('Failed to parse AI insights:', parseError);
            }

            return this.generateInsights(); // Fallback

        } catch (error) {
            console.error('AI analytics failed:', error);
            return this.generateInsights(); // Fallback
        } finally {
            this.setAILoading('analytics', false);
        }
    }

    getTaskSummaryForAI() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(t => t.completed).length;
        const overdue = this.tasks.filter(t => !t.completed && this.isOverdue(t.dueDate)).length;

        const categoryBreakdown = this.tasks.reduce((acc, task) => {
            acc[task.category] = (acc[task.category] || 0) + 1;
            return acc;
        }, {});

        const priorityBreakdown = this.tasks.reduce((acc, task) => {
            acc[task.priority] = (acc[task.priority] || 0) + 1;
            return acc;
        }, {});

        const recentActivity = this.tasks
            .filter(t => {
                const taskDate = new Date(t.createdAt);
                const daysDiff = (Date.now() - taskDate) / (1000 * 60 * 60 * 24);
                return daysDiff <= 7;
            })
            .length;

        return `
Task Statistics:
- Total tasks: ${total}
- Completed: ${completed} (${total > 0 ? Math.round((completed/total)*100) : 0}%)
- Overdue: ${overdue}
- Recent activity (7 days): ${recentActivity} tasks

Category breakdown: ${JSON.stringify(categoryBreakdown)}
Priority breakdown: ${JSON.stringify(priorityBreakdown)}

Current streak: ${this.analytics.streak} days
Productivity score: ${this.calculateProductivityScore()}
        `.trim();
    }

    async showAIInsights() {
        try {
            const insights = await this.generateAIAnalyticsInsights();

            if (insights && insights.length > 0) {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-2xl mx-4 animate-bounce-in max-h-96 overflow-y-auto">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-chart-line text-yellow-500 mr-2"></i>
                                AI Productivity Insights
                            </h3>
                            <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="space-y-3">
                            ${insights.map((insight, index) => `
                                <div class="p-3 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg border-l-4 border-yellow-500">
                                    <div class="flex items-start">
                                        <span class="flex-shrink-0 w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                                            ${index + 1}
                                        </span>
                                        <p class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">${insight}</p>
                                    </div>
                                </div>
                            `).join('')}
                        </div>

                        <div class="mt-6 text-center">
                            <button onclick="this.closest('.fixed').remove()"
                                    class="px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors">
                                Got it!
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
            } else {
                this.showNotification('No insights available at the moment', 'info');
            }

        } catch (error) {
            console.error('Failed to show AI insights:', error);
            this.showNotification('AI insights temporarily unavailable', 'error');
        }
    }

    // Privacy and Security
    showPrivacyDetails() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-2xl mx-4 animate-bounce-in max-h-96 overflow-y-auto">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-shield-alt text-blue-500 mr-2"></i>
                        AI Privacy & Security
                    </h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="space-y-4 text-sm text-gray-700 dark:text-gray-300">
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2">What data is sent to AI?</h4>
                        <ul class="list-disc list-inside space-y-1 ml-4">
                            <li>Task titles and descriptions for categorization and enhancement</li>
                            <li>Aggregated task statistics for productivity insights</li>
                            <li>Voice input for natural language processing</li>
                        </ul>
                    </div>

                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2">Data Protection</h4>
                        <ul class="list-disc list-inside space-y-1 ml-4">
                            <li>Personal information is automatically anonymized</li>
                            <li>API keys are stored locally and encrypted</li>
                            <li>No data is permanently stored by AI providers</li>
                            <li>All communication uses HTTPS encryption</li>
                        </ul>
                    </div>

                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2">Your Control</h4>
                        <ul class="list-disc list-inside space-y-1 ml-4">
                            <li>AI features can be disabled at any time</li>
                            <li>Individual AI features can be toggled independently</li>
                            <li>Data anonymization can be enabled/disabled</li>
                            <li>All data remains local when AI is disabled</li>
                        </ul>
                    </div>

                    <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                        <p class="text-green-800 dark:text-green-300 text-xs">
                            <i class="fas fa-check-circle mr-1"></i>
                            This application prioritizes your privacy and gives you full control over your data.
                        </p>
                    </div>
                </div>

                <div class="mt-6 text-center">
                    <button onclick="this.closest('.fixed').remove()"
                            class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        Understood
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    // Enhanced Error Handling with Fallbacks
    async executeWithFallback(aiFunction, fallbackFunction, context = '') {
        if (!this.aiService || !this.aiService.aiEnabled) {
            return fallbackFunction();
        }

        try {
            return await aiFunction();
        } catch (error) {
            console.error(`AI function failed (${context}):`, error);

            // Show user-friendly error message
            if (error.message.includes('API key')) {
                this.showNotification('Please configure your OpenRouter API key', 'error');
            } else if (error.message.includes('rate limit')) {
                this.showNotification('AI service busy, using fallback', 'info');
            } else {
                this.showNotification('AI temporarily unavailable, using fallback', 'info');
            }

            return fallbackFunction();
        }
    }

    // Initialize AI consent check
    checkAIConsent() {
        const consent = localStorage.getItem('ai_consent');
        if (!consent && this.aiService) {
            this.showAIConsentModal();
        }
    }

    showAIConsentModal() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md mx-4 animate-bounce-in">
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-brain text-purple-500 text-2xl"></i>
                    </div>

                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Enable AI Features?</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-6">
                        This app can use AI to enhance your productivity with smart suggestions,
                        categorization, and insights. Your data will be processed securely and anonymized.
                    </p>

                    <div class="flex space-x-3">
                        <button onclick="todoApp.handleAIConsent(false); this.closest('.fixed').remove();"
                                class="flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                            No Thanks
                        </button>
                        <button onclick="todoApp.handleAIConsent(true); this.closest('.fixed').remove();"
                                class="flex-1 px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                            Enable AI
                        </button>
                    </div>

                    <button onclick="todoApp.showPrivacyDetails()"
                            class="mt-3 text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 underline">
                        Privacy Details
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    handleAIConsent(granted) {
        localStorage.setItem('ai_consent', granted ? 'granted' : 'denied');

        if (granted) {
            this.showNotification('AI features enabled! Configure your API key in settings.', 'success');
            // Show AI settings after a brief delay
            setTimeout(() => this.showAISettings(), 1000);
        } else {
            this.showNotification('AI features disabled. You can enable them later in settings.', 'info');
            if (this.aiService) {
                this.aiService.updateSettings({ enabled: false });
            }
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.todoApp = new TodoApp();

    // Add keyboard shortcut hints
    const helpText = document.createElement('div');
    helpText.className = 'fixed bottom-4 left-4 text-xs text-gray-500 dark:text-gray-400 hidden md:block';
    helpText.innerHTML = `
        <div class="bg-white dark:bg-gray-800 p-2 rounded shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="font-semibold mb-1">Keyboard Shortcuts:</div>
            <div>Ctrl+N: New task</div>
            <div>Ctrl+F: Search</div>
            <div>Ctrl+D: Toggle theme</div>
            <div>Esc: Close/Clear</div>
        </div>
    `;
    document.body.appendChild(helpText);
});
