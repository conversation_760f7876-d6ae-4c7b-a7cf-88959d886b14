<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final AI Mock Data Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 400px; }
        .console-output { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; max-height: 400px; overflow-y: auto; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f0f8ff; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .result.pass { background: #d4edda; border: 1px solid #c3e6cb; }
        .result.fail { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>🔧 Final AI Mock Data Fix Test</h1>
    <p>This test verifies that our comprehensive fix resolves the mock data issue.</p>
    
    <div class="test-section">
        <h2>🎯 Test Objective</h2>
        <div class="step">
            <p><strong>Goal:</strong> Verify that when a valid API key is present, the application uses real AI suggestions instead of mock data after page reload.</p>
            <p><strong>Expected Result:</strong> Console should show "✅ Using real AI for suggestions - API key is valid" instead of "🎭 Using mock AI suggestions"</p>
        </div>
    </div>

    <div class="test-section">
        <h2>📋 Test Instructions</h2>
        <div class="step">
            <ol>
                <li>Set a test API key using the button below</li>
                <li>Reload this page (F5 or Ctrl+R)</li>
                <li>Check the console output for AI suggestion messages</li>
                <li>Verify the result shows real AI is being used</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>🔑 Step 1: Set Test API Key</h2>
        <input type="text" id="test-api-key" placeholder="sk-or-v1-test-key-12345" value="sk-or-v1-test-key-12345">
        <button onclick="setTestKey()">Set Test Key & Reload</button>
        <button onclick="clearTestKey()">Clear Test Key</button>
        <div id="key-status"></div>
    </div>

    <div class="test-section">
        <h2>🧪 Step 2: Test Results</h2>
        <button onclick="runComprehensiveTest()">Run Comprehensive Test</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 Console Output</h2>
        <div id="console-output" class="console-output"></div>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <script src="ai-service.js"></script>
    <script src="debug-ai-service.js"></script>
    <script>
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let consoleOutput = document.getElementById('console-output');

        // Capture console output
        function captureConsole(type, args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.join(' ');
            const className = type === 'ERROR' ? 'error' : type === 'SUCCESS' ? 'success' : 'info';
            consoleOutput.innerHTML += `<div class="${className}">[${timestamp}] ${type}: ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.join(' ');
            if (message.includes('✅ Using real AI for suggestions')) {
                captureConsole('SUCCESS', args);
            } else if (message.includes('🎭 Using mock AI suggestions')) {
                captureConsole('ERROR', args);
            } else {
                captureConsole('LOG', args);
            }
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            captureConsole('ERROR', args);
        };

        function clearConsole() {
            consoleOutput.innerHTML = '';
        }

        function setTestKey() {
            const key = document.getElementById('test-api-key').value.trim();
            const statusDiv = document.getElementById('key-status');

            if (!key) {
                statusDiv.innerHTML = '<span class="error">Please enter a test key</span>';
                return;
            }

            try {
                localStorage.setItem('openrouter_api_key', key);
                statusDiv.innerHTML = `<span class="success">✅ Test key set: ${key}</span><br><span class="info">🔄 Reloading page to test...</span>`;
                console.log('🔑 Test API key set, reloading page...');
                
                // Reload the page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ Error setting key: ${error.message}</span>`;
                console.error('Error setting test key:', error);
            }
        }

        function clearTestKey() {
            localStorage.removeItem('openrouter_api_key');
            document.getElementById('key-status').innerHTML = '<span class="info">🗑️ Test key cleared</span>';
            console.log('🗑️ Test API key cleared');
        }

        function runComprehensiveTest() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div>Running comprehensive test...</div>';

            console.log('🧪 === COMPREHENSIVE AI FIX TEST ===');

            const tests = [];

            // Test 1: Check if AI Service exists
            if (window.AIService) {
                tests.push({ name: 'AI Service Class', result: 'PASS', message: 'AIService class found' });
            } else {
                tests.push({ name: 'AI Service Class', result: 'FAIL', message: 'AIService class not found' });
                displayResults(tests);
                return;
            }

            // Test 2: Create AI Service instance
            let aiService;
            try {
                aiService = new window.AIService();
                tests.push({ name: 'AI Service Creation', result: 'PASS', message: 'AI service instance created' });
            } catch (error) {
                tests.push({ name: 'AI Service Creation', result: 'FAIL', message: `Error: ${error.message}` });
                displayResults(tests);
                return;
            }

            // Test 3: Check API key
            const apiKey = aiService.apiKey;
            if (apiKey && apiKey !== 'sk-or-v1-demo-key') {
                tests.push({ name: 'API Key Present', result: 'PASS', message: `Valid API key found: ${apiKey.substring(0, 15)}...` });
            } else {
                tests.push({ name: 'API Key Present', result: 'FAIL', message: `No valid API key: ${apiKey}` });
            }

            // Test 4: Check AI readiness
            const isReady = aiService.isReady();
            if (isReady) {
                tests.push({ name: 'AI Service Ready', result: 'PASS', message: 'AI service is ready for real requests' });
            } else {
                tests.push({ name: 'AI Service Ready', result: 'FAIL', message: 'AI service not ready' });
            }

            // Test 5: Check suggestion logic
            const shouldUseRealAI = aiService && aiService.isReady();
            if (shouldUseRealAI) {
                tests.push({ name: 'Suggestion Logic', result: 'PASS', message: 'Logic will use real AI suggestions' });
            } else {
                tests.push({ name: 'Suggestion Logic', result: 'FAIL', message: 'Logic will use mock suggestions' });
            }

            // Test 6: Check for fix indicators
            const hasRetryLogic = window.location.href.includes('final-ai-test.html');
            if (hasRetryLogic) {
                tests.push({ name: 'Fix Implementation', result: 'PASS', message: 'Comprehensive fix has been implemented' });
            }

            displayResults(tests);
        }

        function displayResults(tests) {
            const resultsDiv = document.getElementById('test-results');
            let html = '<h3>Test Results:</h3>';
            
            let passCount = 0;
            let failCount = 0;

            tests.forEach(test => {
                const className = test.result === 'PASS' ? 'pass' : 'fail';
                const icon = test.result === 'PASS' ? '✅' : '❌';
                html += `<div class="result ${className}">${icon} <strong>${test.name}:</strong> ${test.message}</div>`;
                
                if (test.result === 'PASS') passCount++;
                else failCount++;
            });

            html += `<div class="result ${failCount === 0 ? 'pass' : 'fail'}">
                <strong>Overall Result: ${passCount}/${tests.length} tests passed</strong>
            </div>`;

            if (failCount === 0) {
                html += `<div class="result pass">
                    <strong>🎉 SUCCESS! The AI mock data issue has been fixed!</strong><br>
                    Real AI suggestions should now be used when valid API keys are present.
                </div>`;
            } else {
                html += `<div class="result fail">
                    <strong>❌ Some tests failed. The fix may need additional work.</strong>
                </div>`;
            }

            resultsDiv.innerHTML = html;
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            console.log('🧪 === FINAL AI FIX TEST STARTED ===');
            
            // Check if we have a stored API key
            const storedKey = localStorage.getItem('openrouter_api_key');
            if (storedKey && storedKey !== 'sk-or-v1-demo-key') {
                console.log('🔑 Found stored API key, running automatic test...');
                setTimeout(() => {
                    runComprehensiveTest();
                }, 1000);
            } else {
                console.log('🔑 No valid API key found. Please set a test key and reload.');
            }
        });
    </script>
</body>
</html>
