<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive AI Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 400px; }
        .console-output { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; max-height: 300px; overflow-y: auto; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f0f8ff; }
    </style>
</head>
<body>
    <h1>Comprehensive AI Mock Data Issue Test</h1>
    <p>This test will help us identify exactly why real AI suggestions aren't being used.</p>
    
    <div class="test-section">
        <h2>Step 1: Set Up Test API Key</h2>
        <div class="step">
            <p>First, let's set up a test API key to simulate having a valid key:</p>
            <input type="text" id="test-api-key" placeholder="sk-or-v1-test-key-12345" value="sk-or-v1-test-key-12345">
            <button onclick="setTestKey()">Set Test Key</button>
            <button onclick="clearTestKey()">Clear Test Key</button>
            <div id="key-status"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>Step 2: Test AI Service Creation</h2>
        <div class="step">
            <p>Test creating a new AI service instance (simulates page reload):</p>
            <button onclick="testAIServiceCreation()">Test AI Service Creation</button>
            <div id="ai-creation-status"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>Step 3: Test Suggestion Logic</h2>
        <div class="step">
            <p>Test the exact logic used in generateInitialAISuggestions:</p>
            <button onclick="testSuggestionLogic()">Test Suggestion Logic</button>
            <div id="suggestion-logic-status"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>Step 4: Test Real AI Request</h2>
        <div class="step">
            <p>Test making an actual AI request (will fail with test key but shows the flow):</p>
            <button onclick="testRealAIRequest()">Test Real AI Request</button>
            <div id="ai-request-status"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <div id="console-output" class="console-output"></div>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <script>
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let consoleOutput = document.getElementById('console-output');

        // Capture console output
        function captureConsole(type, args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.join(' ');
            consoleOutput.innerHTML += `<div>[${timestamp}] ${type}: ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            captureConsole('LOG', args);
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            captureConsole('ERROR', args);
        };

        function clearConsole() {
            consoleOutput.innerHTML = '';
        }

        function setTestKey() {
            const key = document.getElementById('test-api-key').value.trim();
            const statusDiv = document.getElementById('key-status');

            if (!key) {
                statusDiv.innerHTML = '<span class="error">Please enter a test key</span>';
                return;
            }

            try {
                localStorage.setItem('openrouter_api_key', key);
                statusDiv.innerHTML = `<span class="success">✅ Test key set: ${key}</span>`;
                console.log('🔑 Test API key set:', key);
            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ Error setting key: ${error.message}</span>`;
                console.error('Error setting test key:', error);
            }
        }

        function clearTestKey() {
            localStorage.removeItem('openrouter_api_key');
            document.getElementById('key-status').innerHTML = '<span class="info">🗑️ Test key cleared</span>';
            console.log('🗑️ Test API key cleared');
        }

        function testAIServiceCreation() {
            const statusDiv = document.getElementById('ai-creation-status');
            statusDiv.innerHTML = '<div>Testing AI service creation...</div>';

            console.log('🧪 === AI SERVICE CREATION TEST ===');

            try {
                // Check if AIService class exists
                if (!window.AIService) {
                    statusDiv.innerHTML = '<span class="error">❌ AIService class not found</span>';
                    console.error('AIService class not found');
                    return;
                }

                // Create new AI service instance
                console.log('Creating new AI service instance...');
                const aiService = new window.AIService();
                
                // Check the results
                const results = {
                    created: !!aiService,
                    apiKey: aiService.apiKey,
                    aiEnabled: aiService.aiEnabled,
                    isReady: aiService.isReady()
                };

                console.log('AI Service creation results:', results);

                statusDiv.innerHTML = `
                    <div><strong>AI Service Creation Results:</strong></div>
                    <div>• Created: ${results.created ? '✅' : '❌'}</div>
                    <div>• API Key: ${results.apiKey}</div>
                    <div>• AI Enabled: ${results.aiEnabled ? '✅' : '❌'}</div>
                    <div>• Is Ready: ${results.isReady ? '✅' : '❌'}</div>
                `;

                if (results.isReady) {
                    statusDiv.innerHTML += '<div class="success"><strong>🎉 AI Service is ready for real requests!</strong></div>';
                } else {
                    statusDiv.innerHTML += '<div class="error"><strong>❌ AI Service is NOT ready - will use mock data</strong></div>';
                }

            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ Error creating AI service: ${error.message}</span>`;
                console.error('Error creating AI service:', error);
            }
        }

        function testSuggestionLogic() {
            const statusDiv = document.getElementById('suggestion-logic-status');
            statusDiv.innerHTML = '<div>Testing suggestion logic...</div>';

            console.log('🧪 === SUGGESTION LOGIC TEST ===');

            try {
                // Create AI service
                const aiService = new window.AIService();
                
                // Test the exact logic from generateInitialAISuggestions
                const hasAIService = !!aiService;
                const isReady = aiService ? aiService.isReady() : false;
                const shouldUseRealAI = hasAIService && isReady;

                console.log('Suggestion logic evaluation:', {
                    hasAIService,
                    isReady,
                    shouldUseRealAI
                });

                statusDiv.innerHTML = `
                    <div><strong>Suggestion Logic Test:</strong></div>
                    <div>• Has AI Service: ${hasAIService ? '✅' : '❌'}</div>
                    <div>• AI Service Ready: ${isReady ? '✅' : '❌'}</div>
                    <div><strong>• Should Use Real AI: ${shouldUseRealAI ? '✅ YES' : '❌ NO (will use mock)'}</strong></div>
                `;

                if (shouldUseRealAI) {
                    statusDiv.innerHTML += '<div class="success">🎉 Logic says: Use real AI suggestions!</div>';
                } else {
                    statusDiv.innerHTML += '<div class="error">🎭 Logic says: Use mock suggestions</div>';
                    
                    // Debug why it's not ready
                    if (!hasAIService) {
                        statusDiv.innerHTML += '<div class="error">Reason: No AI service</div>';
                    } else if (!isReady) {
                        statusDiv.innerHTML += '<div class="error">Reason: AI service not ready</div>';
                        const status = aiService.getReadinessStatus();
                        statusDiv.innerHTML += `<div class="info">Debug: ${JSON.stringify(status, null, 2)}</div>`;
                    }
                }

            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ Error testing suggestion logic: ${error.message}</span>`;
                console.error('Error testing suggestion logic:', error);
            }
        }

        function testRealAIRequest() {
            const statusDiv = document.getElementById('ai-request-status');
            statusDiv.innerHTML = '<div>Testing real AI request...</div>';

            console.log('🧪 === REAL AI REQUEST TEST ===');

            try {
                const aiService = new window.AIService();
                
                if (!aiService.isReady()) {
                    statusDiv.innerHTML = '<div class="error">❌ AI service not ready - cannot test real request</div>';
                    return;
                }

                // Test making a real request (will likely fail with test key, but shows the flow)
                console.log('Attempting to make real AI request...');
                
                aiService.makeRequest('Test prompt for debugging', {
                    maxTokens: 50,
                    temperature: 0.7
                }).then(response => {
                    console.log('✅ Real AI request succeeded:', response);
                    statusDiv.innerHTML = '<div class="success">✅ Real AI request succeeded! This means the issue is fixed.</div>';
                }).catch(error => {
                    console.log('❌ Real AI request failed (expected with test key):', error.message);
                    statusDiv.innerHTML = `
                        <div class="info">📡 Real AI request was attempted but failed (expected with test key)</div>
                        <div class="info">Error: ${error.message}</div>
                        <div class="success">✅ This means the logic is working - it's trying to use real AI!</div>
                    `;
                });

            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ Error testing real AI request: ${error.message}</span>`;
                console.error('Error testing real AI request:', error);
            }
        }

        // Auto-run initial test
        window.addEventListener('load', () => {
            console.log('🧪 === COMPREHENSIVE AI TEST STARTED ===');
            console.log('Use the buttons above to run each test step.');
        });
    </script>
</body>
</html>
