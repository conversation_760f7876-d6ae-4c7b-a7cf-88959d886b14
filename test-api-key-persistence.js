// Test script to verify API key persistence fix
console.log('=== API Key Persistence Test ===');

// Test 1: Check if localStorage is working
console.log('1. Testing localStorage functionality...');
try {
    localStorage.setItem('test_key', 'test_value');
    const retrieved = localStorage.getItem('test_key');
    console.log('✓ localStorage working:', retrieved === 'test_value');
    localStorage.removeItem('test_key');
} catch (error) {
    console.error('✗ localStorage error:', error);
}

// Test 2: Check AI Service initialization
console.log('2. Testing AI Service initialization...');
if (window.AIService) {
    console.log('✓ AI Service class available');
    
    try {
        const aiService = new window.AIService();
        console.log('✓ AI Service instance created');
        console.log('  - Base URL:', aiService.baseURL);
        console.log('  - API Key:', aiService.apiKey ? 'Present' : 'Missing');
        console.log('  - AI Enabled:', aiService.aiEnabled);
    } catch (error) {
        console.error('✗ AI Service initialization error:', error);
    }
} else {
    console.error('✗ AI Service class not available');
}

// Test 3: Check TodoApp initialization
console.log('3. Testing TodoApp initialization...');
if (window.todoApp) {
    console.log('✓ TodoApp instance available');
    console.log('  - AI Service:', window.todoApp.aiService ? 'Present' : 'Missing');
    
    if (window.todoApp.aiService) {
        console.log('  - AI Service API Key:', window.todoApp.aiService.apiKey);
        console.log('  - AI Service Enabled:', window.todoApp.aiService.aiEnabled);
    }
} else {
    console.error('✗ TodoApp instance not available');
}

// Test 4: Check API key input field population
console.log('4. Testing API key input field...');
const apiKeyInput = document.getElementById('api-key-input');
if (apiKeyInput) {
    console.log('✓ API key input field found');
    console.log('  - Current value:', apiKeyInput.value || '(empty)');
    
    // Check if stored API key exists
    const storedKey = localStorage.getItem('openrouter_api_key');
    console.log('  - Stored API key:', storedKey || '(none)');
    
    if (storedKey && storedKey !== 'sk-or-v1-demo-key') {
        if (apiKeyInput.value === storedKey) {
            console.log('✓ API key properly loaded into input field');
        } else {
            console.error('✗ API key not loaded into input field');
        }
    } else {
        console.log('ℹ No valid API key stored');
    }
} else {
    console.error('✗ API key input field not found');
}

// Test 5: Simulate API key persistence test
console.log('5. Simulating API key persistence...');
function testApiKeyPersistence() {
    const testKey = 'sk-or-v1-test-key-12345';
    
    // Store test key
    localStorage.setItem('openrouter_api_key', testKey);
    console.log('  - Stored test API key');
    
    // Create new AI service instance (simulating page refresh)
    if (window.AIService) {
        const newAiService = new window.AIService();
        if (newAiService.apiKey === testKey) {
            console.log('✓ API key persisted correctly');
        } else {
            console.error('✗ API key not persisted:', newAiService.apiKey);
        }
        
        // Clean up
        localStorage.removeItem('openrouter_api_key');
        console.log('  - Cleaned up test key');
    }
}

testApiKeyPersistence();

console.log('=== Test Complete ===');
