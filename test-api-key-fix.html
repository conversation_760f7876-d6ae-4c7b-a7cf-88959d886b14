<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Key Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 400px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-pass { background: #d4edda; border: 1px solid #c3e6cb; }
        .test-fail { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>API Key Persistence Fix Test</h1>
    
    <div class="test-section">
        <h2>Test 1: API Key Validation</h2>
        <p>Test various API key formats to ensure proper validation:</p>
        
        <div>
            <label>Test API Key:</label>
            <input type="text" id="test-key-input" placeholder="Enter test API key">
            <button onclick="testValidation()">Test Validation</button>
        </div>
        
        <div id="validation-results"></div>
        
        <h3>Quick Tests:</h3>
        <button onclick="testKey('')">Test Empty Key</button>
        <button onclick="testKey('sk-or-v1-demo-key')">Test Demo Key</button>
        <button onclick="testKey('invalid-key')">Test Invalid Format</button>
        <button onclick="testKey('sk-or-v1-')">Test Too Short</button>
        <button onclick="testKey('sk-or-v1-valid-test-key-12345')">Test Valid Format</button>
    </div>

    <div class="test-section">
        <h2>Test 2: Storage and Retrieval</h2>
        <p>Test that valid API keys are stored and invalid ones are rejected:</p>
        
        <button onclick="testStorageFlow()">Run Storage Test</button>
        <div id="storage-results"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: UI Integration</h2>
        <p>Test that the UI properly handles API key loading and validation:</p>
        
        <button onclick="testUIIntegration()">Test UI Integration</button>
        <div id="ui-results"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: Persistence Simulation</h2>
        <p>Simulate page refresh to test persistence:</p>
        
        <button onclick="simulatePersistence()">Simulate Page Refresh</button>
        <div id="persistence-results"></div>
    </div>

    <script src="ai-service.js"></script>
    <script>
        let testResults = [];

        function logResult(test, passed, message) {
            testResults.push({ test, passed, message });
            console.log(`${test}: ${passed ? 'PASS' : 'FAIL'} - ${message}`);
        }

        function displayResult(containerId, test, passed, message) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
            resultDiv.innerHTML = `<strong>${test}:</strong> ${passed ? '✓ PASS' : '✗ FAIL'} - ${message}`;
            container.appendChild(resultDiv);
        }

        function testKey(key) {
            const input = document.getElementById('test-key-input');
            input.value = key;
            testValidation();
        }

        function testValidation() {
            const key = document.getElementById('test-key-input').value;
            const resultsContainer = document.getElementById('validation-results');
            
            if (!window.AIService) {
                displayResult('validation-results', 'AI Service Check', false, 'AI Service not available');
                return;
            }

            const aiService = new window.AIService();
            const validation = aiService.validateAPIKey(key);
            
            const testName = `Validation for "${key || '(empty)'}"`;
            displayResult('validation-results', testName, validation.isValid, 
                validation.isValid ? 'Valid format' : validation.error);
        }

        function testStorageFlow() {
            const resultsContainer = document.getElementById('storage-results');
            resultsContainer.innerHTML = '';

            if (!window.AIService) {
                displayResult('storage-results', 'AI Service Check', false, 'AI Service not available');
                return;
            }

            const aiService = new window.AIService();
            
            // Test 1: Valid key storage
            const validKey = 'sk-or-v1-test-valid-key-12345';
            try {
                aiService.setAPIKey(validKey);
                const stored = localStorage.getItem('openrouter_api_key');
                displayResult('storage-results', 'Valid Key Storage', stored === validKey, 
                    stored === validKey ? 'Valid key stored correctly' : `Expected ${validKey}, got ${stored}`);
            } catch (error) {
                displayResult('storage-results', 'Valid Key Storage', false, `Unexpected error: ${error.message}`);
            }

            // Test 2: Invalid key rejection
            const invalidKey = 'invalid-key-format';
            try {
                aiService.setAPIKey(invalidKey);
                displayResult('storage-results', 'Invalid Key Rejection', false, 'Invalid key was accepted (should have been rejected)');
            } catch (error) {
                displayResult('storage-results', 'Invalid Key Rejection', true, `Invalid key properly rejected: ${error.message}`);
            }

            // Test 3: Retrieval after storage
            const retrievedKey = aiService.getAPIKey();
            displayResult('storage-results', 'Key Retrieval', retrievedKey === validKey,
                retrievedKey === validKey ? 'Key retrieved correctly' : `Expected ${validKey}, got ${retrievedKey}`);

            // Clean up
            localStorage.removeItem('openrouter_api_key');
        }

        function testUIIntegration() {
            const resultsContainer = document.getElementById('ui-results');
            resultsContainer.innerHTML = '';

            // Test if we're in the main app context
            if (window.todoApp && window.todoApp.aiService) {
                displayResult('ui-results', 'Todo App Context', true, 'Running in Todo App context');
                
                // Test loadAISettings function
                if (typeof window.todoApp.loadAISettings === 'function') {
                    displayResult('ui-results', 'loadAISettings Function', true, 'loadAISettings function available');
                    
                    // Test API key input field
                    const apiKeyInput = document.getElementById('api-key-input');
                    if (apiKeyInput) {
                        displayResult('ui-results', 'API Key Input Field', true, 'API key input field found');
                        
                        // Test that demo key is not shown
                        window.todoApp.loadAISettings();
                        const inputValue = apiKeyInput.value;
                        displayResult('ui-results', 'Demo Key Not Shown', inputValue !== 'sk-or-v1-demo-key',
                            inputValue !== 'sk-or-v1-demo-key' ? 'Demo key not shown in input' : 'Demo key incorrectly shown');
                    } else {
                        displayResult('ui-results', 'API Key Input Field', false, 'API key input field not found');
                    }
                } else {
                    displayResult('ui-results', 'loadAISettings Function', false, 'loadAISettings function not available');
                }
            } else {
                displayResult('ui-results', 'Todo App Context', false, 'Not running in Todo App context - open this test from the main app');
            }
        }

        function simulatePersistence() {
            const resultsContainer = document.getElementById('persistence-results');
            resultsContainer.innerHTML = '';

            const testKey = 'sk-or-v1-persistence-test-' + Date.now();
            
            // Store a test key
            localStorage.setItem('openrouter_api_key', testKey);
            displayResult('persistence-results', 'Key Storage', true, `Stored test key: ${testKey}`);

            // Create new AI service instance (simulates page refresh)
            if (window.AIService) {
                const newAiService = new window.AIService();
                const retrievedKey = newAiService.getAPIKey();
                
                displayResult('persistence-results', 'Key Persistence', retrievedKey === testKey,
                    retrievedKey === testKey ? 'Key persisted correctly after "refresh"' : 
                    `Key not persisted. Expected: ${testKey}, Got: ${retrievedKey}`);

                // Test that the key is valid for AI operations
                const isValidForAI = retrievedKey !== 'sk-or-v1-demo-key' && retrievedKey === testKey;
                displayResult('persistence-results', 'AI Ready State', isValidForAI,
                    isValidForAI ? 'AI service ready with persisted key' : 'AI service not ready with persisted key');
            } else {
                displayResult('persistence-results', 'AI Service Creation', false, 'Could not create new AI service instance');
            }

            // Clean up
            localStorage.removeItem('openrouter_api_key');
            displayResult('persistence-results', 'Cleanup', true, 'Test key cleaned up');
        }

        // Run initial tests on page load
        window.addEventListener('load', () => {
            console.log('=== API Key Fix Test Started ===');
            
            // Test basic AI Service availability
            if (window.AIService) {
                console.log('✓ AI Service available');
                const aiService = new window.AIService();
                console.log('✓ AI Service instance created');
                console.log('Current API key:', aiService.apiKey);
            } else {
                console.error('✗ AI Service not available');
            }
        });
    </script>
</body>
</html>
