<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JavaScript Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            color: #22c55e;
            font-weight: bold;
        }
        .error {
            color: #ef4444;
            font-weight: bold;
        }
        .info {
            color: #3b82f6;
            font-weight: bold;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        #console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>JavaScript Error Fixes Test</h1>
    
    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Manual Tests</h2>
        <button onclick="testAsyncFunction()">Test Async Function</button>
        <button onclick="testAIService()">Test AI Service</button>
        <button onclick="testModuleLoading()">Test Module Loading</button>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <div id="console-output"></div>
    </div>

    <!-- Load the actual scripts -->
    <script src="ai-service.js"></script>
    <script src="app.js"></script>

    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const consoleOutput = document.getElementById('console-output');
        
        function logToDiv(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToDiv('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToDiv('error', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToDiv('warn', ...args);
        };

        // Test functions
        function clearConsole() {
            consoleOutput.textContent = '';
        }

        async function testAsyncFunction() {
            console.log('Testing async function...');
            try {
                // Test if we can create a mock async function without errors
                const mockAsyncFunction = async () => {
                    return new Promise(resolve => {
                        setTimeout(() => resolve('Async test successful'), 100);
                    });
                };
                
                const result = await mockAsyncFunction();
                console.log('✅ Async test result:', result);
                return true;
            } catch (error) {
                console.error('❌ Async test failed:', error);
                return false;
            }
        }

        function testAIService() {
            console.log('Testing AI Service...');
            try {
                if (window.AIService) {
                    const aiService = new window.AIService();
                    console.log('✅ AI Service created successfully');
                    console.log('Available models:', aiService.getAvailableModels().length);
                    return true;
                } else {
                    console.error('❌ AI Service not available');
                    return false;
                }
            } catch (error) {
                console.error('❌ AI Service test failed:', error);
                return false;
            }
        }

        function testModuleLoading() {
            console.log('Testing module loading...');
            try {
                // Check if exports is defined (it shouldn't be in browser)
                if (typeof exports !== 'undefined') {
                    console.warn('⚠️ exports is defined (this might cause issues)');
                    return false;
                } else {
                    console.log('✅ exports is not defined (correct for browser)');
                }
                
                // Check if our classes are available
                if (window.AIService && window.todoApp) {
                    console.log('✅ All modules loaded correctly');
                    return true;
                } else {
                    console.error('❌ Some modules missing:', {
                        AIService: !!window.AIService,
                        todoApp: !!window.todoApp
                    });
                    return false;
                }
            } catch (error) {
                console.error('❌ Module loading test failed:', error);
                return false;
            }
        }

        // Run automatic tests
        window.addEventListener('load', async () => {
            const results = document.getElementById('test-results');
            
            console.log('🧪 Starting automatic tests...');
            
            // Wait a bit for everything to load
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const tests = [
                { name: 'Async Function', test: testAsyncFunction },
                { name: 'AI Service', test: testAIService },
                { name: 'Module Loading', test: testModuleLoading }
            ];
            
            let passedTests = 0;
            
            for (const { name, test } of tests) {
                try {
                    const result = await test();
                    const status = result ? 'PASS' : 'FAIL';
                    const className = result ? 'success' : 'error';
                    results.innerHTML += `<div class="${className}">${status}: ${name}</div>`;
                    if (result) passedTests++;
                } catch (error) {
                    results.innerHTML += `<div class="error">ERROR: ${name} - ${error.message}</div>`;
                }
            }
            
            results.innerHTML += `<div class="info">Tests completed: ${passedTests}/${tests.length} passed</div>`;
            console.log(`🏁 Tests completed: ${passedTests}/${tests.length} passed`);
        });
    </script>
</body>
</html>
